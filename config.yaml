# AI-Based Product Name Mapping System Configuration

# AI Models Configuration
ai_models:
  # GPT Models (OpenAI)
  gpt-3.5-turbo-0125: false
  gpt4o: true  # GPT-4o mini
  gpt-4-turbo: false

  # Claude Models (Anthropic)
  claude: true  # Claude 3.5 Sonnet
  claude-3-opus: false

  # Gemini Models (Google)
  gemini: true  # Gemini 2.0 Flash
  gemini-1.5-pro: false

# Model Pricing (USD per 1M tokens)
model_pricing:
  gpt-3.5-turbo-0125:
    input: 0.50
    output: 1.50
    description: "Low-cost, fast responses"
  gpt-4o-mini:
    input: 0.15
    output: 0.60
    description: "Balanced mid-range performance"
  gpt-4-turbo:
    input: 10.00
    output: 30.00
    description: "Highest accuracy and capability"
  claude-3-5-sonnet:
    input: 3.00
    output: 15.00
    description: "Low-cost, fast processing"
  claude-3-opus:
    input: 15.00
    output: 75.00
    description: "Higher accuracy and reasoning"
  gemini-2.0-flash-exp:
    input: 0.075
    output: 0.30
    description: "Fast, very low-cost"
  gemini-1.5-pro:
    input: 1.25
    output: 5.00
    description: "Higher reasoning capability"

# User Interface Settings
ui_settings:
  language: "en"  # "en" for English, "zh-TW" for Traditional Chinese
  remember_settings: true
  log_panel_width: 400  # pixels

# API Keys (will be loaded from environment variables)
api_keys:
  openai_api_key: ${OPENAI_API_KEY}
  anthropic_api_key: ${ANTHROPIC_API_KEY}
  google_api_key: ${GOOGLE_API_KEY}

# Voting Configuration
voting:
  threshold: 2  # 2/3 agreement = match
  confidence_high_threshold: 2
  confidence_low_threshold: 1

# Vendor Configuration
vendor_from: shopify_list_only  # enforce Shopify-only brand matching

# Prompt Configuration
prompt_path: prompts/default_prompt.txt

# Fuzzy Matching Configuration
fuzzy_match:
  top_n_candidates: 5
  score_cutoff: 60  # minimum similarity score

# File Paths
paths:
  store_list: data/store_list.csv
  shopify_list: data/shopify_list.csv
  output_dir: output/
  logs_dir: logs/

# Column Mappings
columns:
  store_list:
    item_description: "Item Description"
    department: "Department"
    modified: "Modified"
  shopify_list:
    title: "Title"
    vendor: "Vendor"
    product_category: "Product Category"

# Brand Abbreviation Mapping
brand_abbreviations:
  SW: "Swisse"
  OST: "Ostelin"
  BL: "Blackmores"
  NAT: "Nature's Own"
  VIT: "Vitamins"
  # Add more mappings as needed

# Logging Configuration
logging:
  level: INFO
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  ai_votes_file: ai_votes.jsonl
  system_log_file: system.log
