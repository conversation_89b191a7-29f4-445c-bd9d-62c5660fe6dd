"""
Streamlit GUI 應用程式 - AI 產品名稱映射系統
Streamlit GUI Application - AI Product Name Mapping System
"""

import streamlit as st
import pandas as pd
import asyncio
import time
import yaml
import logging
import io
from typing import Dict, List, Any, Optional
import os
import json
from datetime import datetime

# 導入自定義模組
from loader import DataLoader
from filter_latest import LatestDataFilter
from brand_map import BrandMapper
from vendor_infer import VendorInferenceEngine
from fuzzy_match import FuzzyMatcher
from prompt_engine import PromptEngine
from ai_clients.client_manager import AIClientManager
from voter import AIVoter
from exporter import ResultExporter
from logger import AILogger
from translations import get_text, get_model_info
from settings_manager import settings_manager


class ProductMappingApp:
    """產品映射應用程式主類"""

    def __init__(self):
        """初始化應用程式"""
        # 載入用戶設定
        self.settings = settings_manager

        # 初始化日誌緩衝區
        if 'log_buffer' not in st.session_state:
            st.session_state.log_buffer = []

        # 設定當前語言
        self.current_language = self.settings.get('language', 'en')

        # 初始化組件
        self.data_loader = None
        self.filter_engine = None
        self.brand_mapper = None
        self.vendor_inferrer = None
        self.fuzzy_matcher = None
        self.prompt_engine = None
        self.ai_manager = None
        self.voter = None
        self.exporter = None
        self.ai_logger = None

        # 初始化組件
        self._initialize_components()

        # 設定日誌處理器
        self._setup_logging()

    def _setup_logging(self):
        """設定日誌處理器"""
        class StreamlitLogHandler(logging.Handler):
            def __init__(self, app_instance):
                super().__init__()
                self.app_instance = app_instance

            def emit(self, record):
                # Get current language from app instance
                current_lang = self.app_instance.current_language

                log_entry = {
                    'timestamp': datetime.now().strftime('%H:%M:%S'),
                    'level': record.levelname,
                    'message': record.getMessage(),
                    'language': current_lang
                }

                # 添加到日誌緩衝區
                if len(st.session_state.log_buffer) >= 1000:
                    st.session_state.log_buffer.pop(0)
                st.session_state.log_buffer.append(log_entry)

        # 添加自定義日誌處理器
        handler = StreamlitLogHandler(self)
        handler.setLevel(logging.INFO)
        logging.getLogger().addHandler(handler)

    def _render_log_panel(self):
        """渲染日誌面板"""
        st.subheader(get_text("log_panel", self.current_language))

        # 日誌控制
        col1, col2, col3 = st.columns(3)

        with col1:
            log_level = st.selectbox(
                get_text("log_level", self.current_language),
                ["DEBUG", "INFO", "WARNING", "ERROR"],
                index=1,
                key="log_level_selector"
            )

        with col2:
            if st.button(get_text("clear_logs", self.current_language)):
                st.session_state.log_buffer = []
                st.rerun()

        with col3:
            if st.button(get_text("export_logs", self.current_language)):
                self._export_logs()

        # 顯示日誌
        log_container = st.container()
        with log_container:
            if st.session_state.log_buffer:
                # 過濾日誌級別
                level_priority = {"DEBUG": 0, "INFO": 1, "WARNING": 2, "ERROR": 3}
                min_level = level_priority[log_level]

                filtered_logs = [
                    log for log in st.session_state.log_buffer
                    if level_priority.get(log['level'], 1) >= min_level
                ]

                # 顯示最新的日誌
                for log_entry in filtered_logs[-50:]:  # 只顯示最新50條
                    level_color = {
                        "DEBUG": "🔍",
                        "INFO": "ℹ️",
                        "WARNING": "⚠️",
                        "ERROR": "❌"
                    }.get(log_entry['level'], "📝")

                    st.text(f"{level_color} {log_entry['timestamp']} {log_entry['message']}")
            else:
                st.info(get_text("info_no_data", self.current_language))

    def _export_logs(self):
        """匯出日誌"""
        if st.session_state.log_buffer:
            log_text = "\n".join([
                f"{log['timestamp']} [{log['level']}] {log['message']}"
                for log in st.session_state.log_buffer
            ])

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"system_logs_{timestamp}.txt"

            st.download_button(
                label=f"📥 {filename}",
                data=log_text,
                file_name=filename,
                mime="text/plain"
            )
    
    def _initialize_components(self):
        """初始化所有組件"""
        try:
            self.data_loader = DataLoader()
            self.filter_engine = LatestDataFilter()
            self.brand_mapper = BrandMapper()
            self.vendor_inferrer = VendorInferenceEngine()
            self.fuzzy_matcher = FuzzyMatcher()
            self.prompt_engine = PromptEngine()
            self.ai_manager = AIClientManager()
            self.voter = AIVoter()
            self.exporter = ResultExporter()
            self.ai_logger = AILogger()

            st.success("所有組件初始化成功！")

        except Exception as e:
            st.error(f"組件初始化失敗: {e}")

    def _initialize_ai_manager(self):
        """重新初始化 AI 管理器"""
        try:
            self.ai_manager = AIClientManager()
            st.success("AI 管理器已更新" if self.current_language == "zh-TW" else "AI Manager updated")
        except Exception as e:
            st.error(f"AI 管理器初始化失敗: {e}")

    def _load_uploaded_file(self, uploaded_file, file_type: str):
        """載入上傳的文件"""
        try:
            file_extension = uploaded_file.name.split('.')[-1].lower()

            if file_extension == 'csv':
                df = pd.read_csv(uploaded_file)
            elif file_extension in ['xlsx', 'xls']:
                df = pd.read_excel(uploaded_file)
            else:
                st.error(f"❌ 不支援的文件格式: {file_extension}")
                return None

            st.success(f"✅ 載入成功！{len(df)} 行資料")
            return df

        except Exception as e:
            st.error(f"❌ 載入失敗: {e}")
            return None

    def _load_existing_file(self, file_path: str, file_type: str):
        """載入現有文件"""
        try:
            file_extension = file_path.split('.')[-1].lower()

            if file_extension == 'csv':
                df = pd.read_csv(file_path)
            elif file_extension in ['xlsx', 'xls']:
                df = pd.read_excel(file_path)
            else:
                st.error(f"❌ 不支援的文件格式: {file_extension}")
                return None

            st.success(f"✅ 載入成功！{len(df)} 行資料")
            return df

        except Exception as e:
            st.error(f"❌ 載入失敗: {e}")
            return None

    def _scan_data_files(self, file_type: str):
        """掃描 data 目錄中的相關文件"""
        import glob
        import os

        data_dir = "data"
        if not os.path.exists(data_dir):
            return []

        # 根據文件類型搜索相關文件
        if file_type == "store":
            patterns = [
                "data/*store*.csv",
                "data/*store*.xlsx",
                "data/*store*.xls",
                "data/Store/*.csv",
                "data/Store/*.xlsx",
                "data/Store/*.xls"
            ]
        else:  # shopify
            patterns = [
                "data/*shopify*.csv",
                "data/*shopify*.xlsx",
                "data/*shopify*.xls",
                "data/Shopify/*.csv",
                "data/Shopify/*.xlsx",
                "data/Shopify/*.xls"
            ]

        files = []
        for pattern in patterns:
            files.extend(glob.glob(pattern))

        # 移除重複並排序
        files = sorted(list(set(files)))
        return files

    def _display_data_preview(self, df: pd.DataFrame, title: str):
        """顯示資料預覽"""
        st.subheader(f"📊 {title} - 資料預覽")

        # 基本統計
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("總行數", len(df))
        with col2:
            st.metric("總欄位數", len(df.columns))
        with col3:
            st.metric("記憶體使用", f"{df.memory_usage(deep=True).sum() / 1024 / 1024:.1f} MB")

        # 欄位信息
        st.subheader("📋 欄位信息")
        column_info = pd.DataFrame({
            '欄位名稱': df.columns,
            '資料類型': df.dtypes.astype(str),
            '非空值數量': df.count(),
            '空值數量': df.isnull().sum(),
            '唯一值數量': df.nunique()
        })
        st.dataframe(column_info, use_container_width=True)

        # 資料預覽
        st.subheader("🔍 資料內容預覽")
        preview_rows = st.slider(f"顯示行數 ({title})", 5, min(50, len(df)), 10, key=f"preview_{title}")
        st.dataframe(df.head(preview_rows), use_container_width=True)

    def _display_vendor_stats(self, shopify_df: pd.DataFrame):
        """顯示供應商統計"""
        if 'Vendor' in shopify_df.columns:
            st.subheader("📈 供應商分佈")
            vendor_counts = shopify_df['Vendor'].value_counts()

            col1, col2 = st.columns(2)

            with col1:
                st.metric("唯一供應商數量", len(vendor_counts))
                st.metric("最大供應商產品數", vendor_counts.iloc[0] if len(vendor_counts) > 0 else 0)

            with col2:
                # 顯示前10個供應商
                st.write("**前10個供應商:**")
                top_vendors = vendor_counts.head(10)
                for vendor, count in top_vendors.items():
                    st.write(f"• {vendor}: {count} 個產品")

            # 圖表顯示
            if len(vendor_counts) > 0:
                st.bar_chart(vendor_counts.head(15))
    
    def run(self):
        """運行 Streamlit 應用程式"""
        st.set_page_config(
            page_title=get_text("app_title", self.current_language),
            page_icon="🧠",
            layout="wide"
        )

        # 創建主要佈局：左側日誌面板，右側主內容
        log_col, main_col = st.columns([1, 2])

        with log_col:
            self._render_log_panel()

        with main_col:
            st.title(get_text("app_title", self.current_language))
            st.markdown(get_text("app_subtitle", self.current_language))

            # 語言選擇器
            col1, col2, col3 = st.columns([1, 1, 2])
            with col1:
                language_options = {"English": "en", "繁體中文": "zh-TW"}
                selected_language = st.selectbox(
                    get_text("language_setting", self.current_language),
                    options=list(language_options.keys()),
                    index=0 if self.current_language == "en" else 1,
                    key="language_selector"
                )

                # 更新語言設定
                new_language = language_options[selected_language]
                if new_language != self.current_language:
                    self.current_language = new_language
                    self.settings.set('language', new_language)
                    st.rerun()

            # 主要內容區域
            tab1, tab2, tab3, tab4 = st.tabs([
                get_text("tab_upload", self.current_language),
                get_text("tab_config", self.current_language),
                get_text("tab_process", self.current_language),
                get_text("tab_results", self.current_language)
            ])

            with tab1:
                self._render_file_upload_tab()

            with tab2:
                self._render_configuration_tab()

            with tab3:
                self._render_processing_tab()

            with tab4:
                self._render_results_tab()
    
    def _render_sidebar(self):
        """渲染側邊欄"""
        st.sidebar.header("🛠️ 系統配置")
        
        # AI 模型選擇
        st.sidebar.subheader("AI 模型選擇")
        
        available_clients = self.ai_manager.get_available_clients() if self.ai_manager else []
        enabled_models = self.ai_manager.get_enabled_models() if self.ai_manager else {}
        
        selected_models = []
        for model in ['gpt4o', 'claude', 'gemini']:
            if model in available_clients:
                if st.sidebar.checkbox(f"GPT-4o mini" if model == 'gpt4o' 
                                     else f"Claude 3.5" if model == 'claude' 
                                     else "Gemini 2.0", 
                                     value=enabled_models.get(model, False),
                                     key=f"model_{model}"):
                    selected_models.append(model)
        
        st.session_state['selected_models'] = selected_models
        
        # 投票閾值設定
        st.sidebar.subheader("投票設定")
        st.session_state['voting_threshold'] = st.sidebar.slider(
            "投票閾值", min_value=1, max_value=3, value=2,
            help="需要多少個模型同意才算達成共識"
        )
        
        # 模糊匹配設定
        st.sidebar.subheader("模糊匹配設定")
        st.session_state['fuzzy_top_n'] = st.sidebar.slider(
            "候選項目數量", min_value=3, max_value=10, value=5
        )
        st.session_state['fuzzy_cutoff'] = st.sidebar.slider(
            "相似度閾值", min_value=50, max_value=95, value=60
        )
    
    def _render_file_upload_tab(self):
        """渲染文件上傳標籤頁"""
        st.header(get_text("upload_header", self.current_language))

        # 添加文件格式說明
        with st.expander(get_text("file_requirements", self.current_language), expanded=False):
            if self.current_language == "zh-TW":
                st.markdown("""
                **支援的文件格式:**
                - CSV 文件 (.csv)
                - Excel 文件 (.xlsx, .xls)

                **藥房清單 (Store List) 必要欄位:**
                - `Item Description` - 產品描述
                - `Department` - 部門/品牌
                - `Modified` - 修改時間

                **Shopify 清單必要欄位:**
                - `Title` - 產品標題
                - `Vendor` - 供應商
                - `Product Category` - 產品類別 (可選)
                """)
            else:
                st.markdown("""
                **Supported File Formats:**
                - CSV files (.csv)
                - Excel files (.xlsx, .xls)

                **Store List Required Columns:**
                - `Item Description` - Product description
                - `Department` - Department/Brand
                - `Modified` - Modification time

                **Shopify List Required Columns:**
                - `Title` - Product title
                - `Vendor` - Vendor/Supplier
                - `Product Category` - Product category (optional)
                """)

        col1, col2 = st.columns(2)

        with col1:
            if self.current_language == "zh-TW":
                st.subheader("🏪 藥房產品清單 (Store List)")
                upload_options = ["上傳文件", "使用現有文件", "使用示例資料"]
                upload_label = "選擇導入方式:"
            else:
                st.subheader("🏪 Store Product List")
                upload_options = ["Upload File", "Use Existing File", "Use Sample Data"]
                upload_label = "Select Import Method:"

            # 文件上傳選項
            upload_option = st.radio(
                upload_label,
                upload_options,
                key="store_upload_option"
            )

            if upload_option in ["上傳文件", "Upload File"]:
                store_file = st.file_uploader(
                    "選擇文件",
                    type=['csv', 'xlsx', 'xls'],
                    key="store_file",
                    help="支援 CSV 和 Excel 格式"
                )

                if store_file is not None:
                    store_df = self._load_uploaded_file(store_file, "store")
                    if store_df is not None:
                        st.session_state['store_df'] = store_df
                        self._display_data_preview(store_df, "藥房清單")

            elif upload_option in ["使用現有文件", "Use Existing File"]:
                # 掃描 data 目錄中的文件
                data_files = self._scan_data_files("store")
                if data_files:
                    selected_file = st.selectbox(
                        "選擇現有文件:",
                        data_files,
                        key="store_existing_file"
                    )

                    if st.button("載入選中的文件", key="load_store_existing"):
                        store_df = self._load_existing_file(selected_file, "store")
                        if store_df is not None:
                            st.session_state['store_df'] = store_df
                            self._display_data_preview(store_df, "藥房清單")
                else:
                    st.info("📁 data 目錄中沒有找到相關文件")

            elif upload_option in ["使用示例資料", "Use Sample Data"]:
                if st.button("載入示例資料", key="load_store_sample"):
                    try:
                        store_df = pd.read_csv("data/store_list_sample.csv")
                        st.session_state['store_df'] = store_df
                        self._display_data_preview(store_df, "藥房清單 (示例)")
                        st.success("✅ 示例資料載入成功！")
                    except FileNotFoundError:
                        st.error("❌ 示例文件不存在，請先運行 'python run.py setup --create-sample'")

        with col2:
            if self.current_language == "zh-TW":
                st.subheader("🛒 Shopify 主清單")
                upload_options = ["上傳文件", "使用現有文件", "使用示例資料"]
                upload_label = "選擇導入方式:"
            else:
                st.subheader("🛒 Shopify Master List")
                upload_options = ["Upload File", "Use Existing File", "Use Sample Data"]
                upload_label = "Select Import Method:"

            # 文件上傳選項
            upload_option = st.radio(
                upload_label,
                upload_options,
                key="shopify_upload_option"
            )

            if upload_option in ["上傳文件", "Upload File"]:
                file_label = "選擇文件" if self.current_language == "zh-TW" else "Choose File"
                file_help = "支援 CSV 和 Excel 格式" if self.current_language == "zh-TW" else "Supports CSV and Excel formats"

                shopify_file = st.file_uploader(
                    file_label,
                    type=['csv', 'xlsx', 'xls'],
                    key="shopify_file",
                    help=file_help
                )

                if shopify_file is not None:
                    shopify_df = self._load_uploaded_file(shopify_file, "shopify")
                    if shopify_df is not None:
                        st.session_state['shopify_df'] = shopify_df
                        self._display_data_preview(shopify_df, "Shopify 清單")
                        self._display_vendor_stats(shopify_df)

            elif upload_option in ["使用現有文件", "Use Existing File"]:
                # 掃描 data 目錄中的文件
                data_files = self._scan_data_files("shopify")
                if data_files:
                    selected_file = st.selectbox(
                        "選擇現有文件:",
                        data_files,
                        key="shopify_existing_file"
                    )

                    if st.button("載入選中的文件", key="load_shopify_existing"):
                        shopify_df = self._load_existing_file(selected_file, "shopify")
                        if shopify_df is not None:
                            st.session_state['shopify_df'] = shopify_df
                            self._display_data_preview(shopify_df, "Shopify 清單")
                            self._display_vendor_stats(shopify_df)
                else:
                    st.info("📁 data 目錄中沒有找到相關文件")

            elif upload_option in ["使用示例資料", "Use Sample Data"]:
                if st.button("載入示例資料", key="load_shopify_sample"):
                    try:
                        shopify_df = pd.read_csv("data/shopify_list_sample.csv")
                        st.session_state['shopify_df'] = shopify_df
                        self._display_data_preview(shopify_df, "Shopify 清單 (示例)")
                        self._display_vendor_stats(shopify_df)
                        st.success("✅ 示例資料載入成功！")
                    except FileNotFoundError:
                        st.error("❌ 示例文件不存在，請先運行 'python run.py setup --create-sample'")
    
    def _render_configuration_tab(self):
        """渲染配置標籤頁"""
        st.header(get_text("config_header", self.current_language))

        # API 設定區域
        self._render_api_settings()

        st.divider()

        # AI 模型選擇
        self._render_model_selection()

        st.divider()

        # 處理選項
        self._render_processing_options()

        st.divider()

        # 成本估算
        self._render_cost_estimate()

        st.divider()

        # 欄位映射
        self._render_column_mapping()

    def _render_api_settings(self):
        """渲染 API 設定"""
        st.subheader(get_text("api_settings", self.current_language))

        # 獲取當前 API 密鑰
        current_keys = self.settings.get_api_keys()

        col1, col2, col3 = st.columns(3)

        with col1:
            st.write("**OpenAI API Key**")
            openai_key = st.text_input(
                "GPT Models",
                value=current_keys.get('openai_api_key', ''),
                type="password",
                key="openai_api_key_input",
                help="Required for GPT models"
            )

        with col2:
            st.write("**Anthropic API Key**")
            anthropic_key = st.text_input(
                "Claude Models",
                value=current_keys.get('anthropic_api_key', ''),
                type="password",
                key="anthropic_api_key_input",
                help="Required for Claude models"
            )

        with col3:
            st.write("**Google API Key**")
            google_key = st.text_input(
                "Gemini Models",
                value=current_keys.get('google_api_key', ''),
                type="password",
                key="google_api_key_input",
                help="Required for Gemini models"
            )

        # 保存 API 密鑰
        if st.button("💾 " + ("Save API Keys" if self.current_language == "en" else "保存 API 密鑰")):
            self.settings.update_api_keys({
                'openai_api_key': openai_key,
                'anthropic_api_key': anthropic_key,
                'google_api_key': google_key
            })
            st.success(get_text("settings_saved", self.current_language))

            # 更新環境變數
            os.environ['OPENAI_API_KEY'] = openai_key
            os.environ['ANTHROPIC_API_KEY'] = anthropic_key
            os.environ['GOOGLE_API_KEY'] = google_key

            # 重新初始化 AI 管理器
            self._initialize_ai_manager()

    def _render_model_selection(self):
        """渲染 AI 模型選擇"""
        st.subheader(get_text("model_selection", self.current_language))

        # 載入模型定價信息
        with open('config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        model_pricing = config.get('model_pricing', {})
        model_info = get_model_info(self.current_language)
        current_selection = self.settings.get_selected_models()

        # GPT 模型
        st.write(f"**{get_text('gpt_models', self.current_language)}**")
        gpt_models = ['gpt-3.5-turbo-0125', 'gpt-4o-mini', 'gpt-4-turbo']

        for model in gpt_models:
            col1, col2, col3, col4 = st.columns([3, 2, 2, 3])

            with col1:
                selected = st.checkbox(
                    self._get_model_display_name(model),
                    value=current_selection.get(model, False),
                    key=f"model_{model}"
                )
                current_selection[model] = selected

            with col2:
                if model in model_pricing:
                    st.text(f"${model_pricing[model]['input']}")

            with col3:
                if model in model_pricing:
                    st.text(f"${model_pricing[model]['output']}")

            with col4:
                st.text(model_info.get(model, ""))

        # Claude 模型
        st.write(f"**{get_text('claude_models', self.current_language)}**")
        claude_models = ['claude-3-5-sonnet', 'claude-3-opus']

        for model in claude_models:
            col1, col2, col3, col4 = st.columns([3, 2, 2, 3])

            with col1:
                selected = st.checkbox(
                    self._get_model_display_name(model),
                    value=current_selection.get(model, False),
                    key=f"model_{model}"
                )
                current_selection[model] = selected

            with col2:
                if model in model_pricing:
                    st.text(f"${model_pricing[model]['input']}")

            with col3:
                if model in model_pricing:
                    st.text(f"${model_pricing[model]['output']}")

            with col4:
                st.text(model_info.get(model, ""))

        # Gemini 模型
        st.write(f"**{get_text('gemini_models', self.current_language)}**")
        gemini_models = ['gemini-2.0-flash-exp', 'gemini-1.5-pro']

        for model in gemini_models:
            col1, col2, col3, col4 = st.columns([3, 2, 2, 3])

            with col1:
                selected = st.checkbox(
                    self._get_model_display_name(model),
                    value=current_selection.get(model, False),
                    key=f"model_{model}"
                )
                current_selection[model] = selected

            with col2:
                if model in model_pricing:
                    st.text(f"${model_pricing[model]['input']}")

            with col3:
                if model in model_pricing:
                    st.text(f"${model_pricing[model]['output']}")

            with col4:
                st.text(model_info.get(model, ""))

        # 保存模型選擇
        self.settings.update_model_selection(current_selection)

    def _get_model_display_name(self, model: str) -> str:
        """獲取模型顯示名稱"""
        display_names = {
            'gpt-3.5-turbo-0125': 'GPT-3.5 Turbo [Low-cost]',
            'gpt-4o-mini': 'GPT-4o Mini [Balanced]',
            'gpt-4-turbo': 'GPT-4 Turbo [High Accuracy]',
            'claude-3-5-sonnet': 'Claude 3.5 Sonnet [Fast]',
            'claude-3-opus': 'Claude 3 Opus [High Accuracy]',
            'gemini-2.0-flash-exp': 'Gemini 2.0 Flash [Very Low-cost]',
            'gemini-1.5-pro': 'Gemini 1.5 Pro [High Reasoning]'
        }
        return display_names.get(model, model)

    def _render_processing_options(self):
        """渲染處理選項"""
        st.subheader(get_text("processing_options", self.current_language))

        current_settings = self.settings.get_processing_settings()

        col1, col2, col3 = st.columns(3)

        with col1:
            # Row range control
            st.write("**" + ("行範圍控制" if self.current_language == "zh-TW" else "Row Range Control") + "**")

            process_all = st.checkbox(
                get_text("process_all_rows", self.current_language),
                value=current_settings.get('process_all_rows', True),
                help="處理所有行或指定範圍" if self.current_language == "zh-TW" else "Process all rows or specify range",
                key="process_all_rows_checkbox"
            )

            if not process_all:
                # Get total rows if data is loaded
                total_rows = 0
                if 'store_df' in st.session_state:
                    total_rows = len(st.session_state['store_df'])
                    st.info(f"總行數: {total_rows}")

                col1a, col1b = st.columns(2)
                with col1a:
                    start_row = st.number_input(
                        "開始行" if self.current_language == "zh-TW" else "Start Row",
                        min_value=1, max_value=max(total_rows, 1),
                        value=current_settings.get('start_row', 1),
                        help="從第幾行開始處理" if self.current_language == "zh-TW" else "Which row to start from"
                    )

                with col1b:
                    end_row = st.number_input(
                        "結束行" if self.current_language == "zh-TW" else "End Row",
                        min_value=start_row, max_value=max(total_rows, start_row),
                        value=current_settings.get('end_row', min(start_row + 99, total_rows)) if total_rows > 0 else start_row + 99,
                        help="處理到第幾行" if self.current_language == "zh-TW" else "Which row to end at"
                    )

                row_limit = end_row - start_row + 1
                st.write(f"將處理 {row_limit} 行 (第 {start_row} 到第 {end_row} 行)")
            else:
                start_row = 1
                end_row = None
                row_limit = None

            voting_threshold = st.slider(
                get_text("voting_threshold", self.current_language),
                min_value=1, max_value=3,
                value=current_settings.get('voting_threshold', 2),
                help="需要多少個模型同意才算達成共識" if self.current_language == "zh-TW" else "How many models need to agree for consensus"
            )

        with col2:
            batch_size = st.number_input(
                get_text("batch_size", self.current_language),
                min_value=1, max_value=50,
                value=current_settings.get('batch_size', 10),
                help="每次處理的項目數量" if self.current_language == "zh-TW" else "Number of items to process at once"
            )

            fuzzy_top_n = st.slider(
                get_text("candidate_count", self.current_language),
                min_value=3, max_value=10,
                value=current_settings.get('fuzzy_top_n', 5)
            )

            filter_duplicates = st.checkbox(
                get_text("filter_duplicates", self.current_language),
                value=current_settings.get('filter_duplicates', True),
                help="根據修改時間保留最新版本" if self.current_language == "zh-TW" else "Keep latest version based on modification time",
                key="filter_duplicates_checkbox"
            )

        with col3:
            fuzzy_cutoff = st.slider(
                get_text("similarity_threshold", self.current_language),
                min_value=50, max_value=95,
                value=current_settings.get('fuzzy_cutoff', 60)
            )

            infer_vendors = st.checkbox(
                get_text("ai_vendor_inference", self.current_language),
                value=current_settings.get('infer_vendors', True),
                help="當品牌信息缺失時使用 AI 推斷" if self.current_language == "zh-TW" else "Use AI to infer vendor when brand information is missing",
                key="infer_vendors_checkbox"
            )

        # 保存處理設定
        updated_settings = {
            'voting_threshold': voting_threshold,
            'batch_size': batch_size,
            'filter_duplicates': filter_duplicates,
            'fuzzy_top_n': fuzzy_top_n,
            'fuzzy_cutoff': fuzzy_cutoff,
            'infer_vendors': infer_vendors,
            'process_all_rows': process_all,
            'row_limit': row_limit,
            'start_row': start_row if not process_all else 1,
            'end_row': end_row if not process_all else None
        }
        self.settings.update_processing_settings(updated_settings)

    def _render_cost_estimate(self):
        """渲染成本估算"""
        st.subheader(get_text("cost_estimate", self.current_language))

        # 檢查是否有資料
        if 'store_df' not in st.session_state:
            st.info("請先上傳資料文件以進行成本估算" if self.current_language == "zh-TW" else "Please upload data files first for cost estimation")
            return

        store_df = st.session_state['store_df']
        selected_models = self.settings.get_selected_models()
        active_models = [model for model, selected in selected_models.items() if selected]
        processing_settings = self.settings.get_processing_settings()

        if not active_models:
            st.warning("請先選擇 AI 模型" if self.current_language == "zh-TW" else "Please select AI models first")
            return

        # 載入定價信息
        with open('config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        model_pricing = config.get('model_pricing', {})

        # 計算實際要處理的行數
        total_rows = len(store_df)
        if not processing_settings.get('process_all_rows', True):
            rows_to_process = min(processing_settings.get('row_limit', 100), total_rows)
        else:
            rows_to_process = total_rows

        # 估算 token 使用量
        avg_description_length = store_df['Item Description'].str.len().mean() if 'Item Description' in store_df.columns else 50
        estimated_input_tokens = (avg_description_length / 4) * rows_to_process  # 粗略估算
        estimated_output_tokens = estimated_input_tokens * 0.1  # 輸出通常較短

        total_cost = 0
        cost_breakdown = {}

        for model in active_models:
            if model in model_pricing:
                pricing = model_pricing[model]
                input_cost = (estimated_input_tokens / 1000000) * pricing['input']
                output_cost = (estimated_output_tokens / 1000000) * pricing['output']
                model_cost = input_cost + output_cost
                total_cost += model_cost
                cost_breakdown[model] = model_cost

        # 顯示成本估算
        col1, col2 = st.columns(2)

        with col1:
            st.metric("總估算成本 / Total Estimated Cost", f"${total_cost:.4f} USD")
            st.metric("處理項目數 / Items to Process", f"{rows_to_process:,} / {total_rows:,}")
            st.metric("每項目成本 / Cost per Item", f"${total_cost/rows_to_process:.6f} USD" if rows_to_process > 0 else "$0.000000 USD")

        with col2:
            st.write("**成本明細 / Cost Breakdown:**")
            for model, cost in cost_breakdown.items():
                st.write(f"• {self._get_model_display_name(model)}: ${cost:.4f}")

        st.info("💡 " + ("這是粗略估算，實際成本可能因內容長度而異" if self.current_language == "zh-TW" else "This is a rough estimate. Actual costs may vary based on content length."))

    def _render_column_mapping(self):
        """渲染欄位映射配置"""
        st.subheader("📋 " + ("欄位映射" if self.current_language == "zh-TW" else "Column Mapping"))

        # 檢查是否已上傳文件
        if 'store_df' not in st.session_state or 'shopify_df' not in st.session_state:
            st.warning("⚠️ " + ("請先上傳兩個資料文件" if self.current_language == "zh-TW" else "Please upload both data files first"))
            return

        store_df = st.session_state['store_df']
        shopify_df = st.session_state['shopify_df']

        col1, col2 = st.columns(2)

        with col1:
            st.write("**" + ("藥房清單欄位映射" if self.current_language == "zh-TW" else "Store List Column Mapping") + "**")
            store_columns = store_df.columns.tolist()

            item_desc_label = "產品描述欄位" if self.current_language == "zh-TW" else "Product Description Column"
            item_desc_col = st.selectbox(
                item_desc_label, store_columns,
                index=store_columns.index('Item Description') if 'Item Description' in store_columns else 0,
                key="store_item_desc"
            )

            dept_label = "部門/品牌欄位" if self.current_language == "zh-TW" else "Department/Brand Column"
            dept_col = st.selectbox(
                dept_label, store_columns,
                index=store_columns.index('Department') if 'Department' in store_columns else 0,
                key="store_dept"
            )

            modified_label = "修改時間欄位" if self.current_language == "zh-TW" else "Modified Time Column"
            modified_col = st.selectbox(
                modified_label, store_columns,
                index=store_columns.index('Modified') if 'Modified' in store_columns else 0,
                key="store_modified"
            )

        with col2:
            st.write("**" + ("Shopify 清單欄位映射" if self.current_language == "zh-TW" else "Shopify List Column Mapping") + "**")
            shopify_columns = shopify_df.columns.tolist()

            title_label = "產品標題欄位" if self.current_language == "zh-TW" else "Product Title Column"
            title_col = st.selectbox(
                title_label, shopify_columns,
                index=shopify_columns.index('Title') if 'Title' in shopify_columns else 0,
                key="shopify_title"
            )

            vendor_label = "供應商欄位" if self.current_language == "zh-TW" else "Vendor Column"
            vendor_col = st.selectbox(
                vendor_label, shopify_columns,
                index=shopify_columns.index('Vendor') if 'Vendor' in shopify_columns else 0,
                key="shopify_vendor"
            )

            category_label = "產品類別欄位" if self.current_language == "zh-TW" else "Product Category Column"
            category_col = st.selectbox(
                category_label, shopify_columns,
                index=shopify_columns.index('Product Category') if 'Product Category' in shopify_columns else 0,
                key="shopify_category"
            )

        # 儲存欄位映射
        column_mapping = {
            'store': {
                'item_description': item_desc_col,
                'department': dept_col,
                'modified': modified_col
            },
            'shopify': {
                'title': title_col,
                'vendor': vendor_col,
                'product_category': category_col
            }
        }

        st.session_state['column_mapping'] = column_mapping
        self.settings.update_column_mapping(column_mapping)
        
        # 顯示配置摘要
        st.subheader("📝 配置摘要")
        st.json({
            'selected_models': st.session_state.get('selected_models', []),
            'voting_threshold': st.session_state.get('voting_threshold', 2),
            'processing_options': st.session_state.get('processing_options', {}),
            'data_summary': {
                'store_rows': len(store_df),
                'shopify_rows': len(shopify_df),
                'unique_vendors': shopify_df[vendor_col].nunique()
            }
        })
    
    def _render_processing_tab(self):
        """渲染處理標籤頁"""
        st.header(get_text("tab_process", self.current_language))

        # 檢查前置條件
        if not self._check_prerequisites():
            return

        # 處理按鈕
        button_text = get_text("start_processing", self.current_language)
        if st.button(button_text, type="primary", use_container_width=True):
            # 使用 asyncio 運行異步處理
            asyncio.run(self._run_processing())
    
    def _check_prerequisites(self) -> bool:
        """檢查處理前置條件"""
        missing_items = []

        if 'store_df' not in st.session_state:
            missing_items.append("藥房產品清單" if self.current_language == "zh-TW" else "Store Product List")

        if 'shopify_df' not in st.session_state:
            missing_items.append("Shopify 主清單" if self.current_language == "zh-TW" else "Shopify Master List")

        # Check if any AI models are selected
        selected_models = self.settings.get_selected_models()
        active_models = [model for model, selected in selected_models.items() if selected]
        if not active_models:
            missing_items.append("AI 模型選擇" if self.current_language == "zh-TW" else "AI Model Selection")

        if 'column_mapping' not in st.session_state:
            missing_items.append("欄位映射配置" if self.current_language == "zh-TW" else "Column Mapping Configuration")

        if missing_items:
            error_msg = "❌ 缺少必要配置: " if self.current_language == "zh-TW" else "❌ Missing required configuration: "
            st.error(f"{error_msg}{', '.join(missing_items)}")
            return False

        return True
    
    async def _run_processing(self):
        """執行實際的處理流程"""
        # 創建進度條
        progress_bar = st.progress(0)
        status_text = st.empty()

        try:
            # 獲取資料和設定
            store_df = st.session_state['store_df']
            shopify_df = st.session_state['shopify_df']
            processing_settings = self.settings.get_processing_settings()
            column_mapping = st.session_state.get('column_mapping', {})

            # 步驟 1: 資料預處理
            status_text.text("步驟 1/6: 資料預處理...")
            progress_bar.progress(10)

            # 應用行範圍設定
            if not processing_settings.get('process_all_rows', True):
                start_row = processing_settings.get('start_row', 1) - 1  # 轉換為 0-based index
                end_row = processing_settings.get('end_row', len(store_df))
                store_df = store_df.iloc[start_row:end_row].copy()

            # 步驟 2: 重複項目過濾
            status_text.text("步驟 2/6: 過濾重複項目...")
            progress_bar.progress(25)

            if processing_settings.get('filter_duplicates', True):
                filtered_df, filter_report = self.filter_engine.filter_and_report(store_df)
            else:
                filtered_df = store_df.copy()
                filter_report = {'removed_rows': 0}

            # 步驟 3: 設定有效供應商
            status_text.text("步驟 3/6: 設定供應商清單...")
            progress_bar.progress(40)

            valid_vendors = self.data_loader.get_unique_vendors(shopify_df)
            self.brand_mapper.set_valid_vendors(valid_vendors)
            self.vendor_inferrer.set_valid_vendors(valid_vendors)
            self.vendor_inferrer.set_ai_clients(self.ai_manager.clients)

            # 步驟 4: 批次處理
            status_text.text("步驟 4/6: 執行產品映射...")
            progress_bar.progress(60)

            results = []
            batch_size = processing_settings.get('batch_size', 10)
            total_items = len(filtered_df)

            for i in range(0, total_items, batch_size):
                batch_end = min(i + batch_size, total_items)
                batch_df = filtered_df.iloc[i:batch_end]

                # 更新進度
                batch_progress = 60 + (30 * (batch_end / total_items))
                progress_bar.progress(int(batch_progress))
                status_text.text(f"步驟 4/6: 處理批次 {i//batch_size + 1} ({i+1}-{batch_end}/{total_items})")

                # 處理批次中的每個項目
                batch_tasks = []
                for _, row in batch_df.iterrows():
                    item_data = row.to_dict()
                    task = self._process_single_item_sync(item_data, shopify_df, valid_vendors)
                    batch_tasks.append(task)

                # 收集批次結果
                for task_result in batch_tasks:
                    results.append(task_result)

            # 步驟 5: 生成摘要
            status_text.text("步驟 5/6: 生成處理摘要...")
            progress_bar.progress(90)

            summary = self._create_summary_report(results)

            # 步驟 6: 完成
            status_text.text("步驟 6/6: 處理完成...")
            progress_bar.progress(100)

            # 保存結果
            processing_data = {
                'results': results,
                'summary': summary,
                'processing_time': time.time(),
                'filter_report': filter_report,
                'settings_used': processing_settings
            }

            st.session_state['processing_results'] = processing_data

            status_text.text("✅ 處理完成！")
            st.success(f"🎉 映射處理已完成！處理了 {len(results)} 個項目，成功匹配 {summary['successful_matches']} 個。")

        except Exception as e:
            st.error(f"❌ 處理失敗: {e}")
            import traceback
            st.error(f"詳細錯誤: {traceback.format_exc()}")

    def _process_single_item_sync(self, item_data: dict, shopify_df, valid_vendors: list) -> dict:
        """同步處理單個項目（簡化版本，不使用 AI）"""
        try:
            item_description = item_data.get('Item Description', '')
            department = item_data.get('Department', '')

            # 步驟 1: 品牌處理
            brand_result = self.brand_mapper.process_brand(department)
            final_vendor = brand_result.get('final_vendor', '')

            # 步驟 2: 模糊匹配
            if final_vendor and final_vendor in valid_vendors:
                fuzzy_matches = self.fuzzy_matcher.match_product_by_vendor(
                    item_description, final_vendor, shopify_df
                )
            else:
                # 在所有產品中搜索
                all_candidates = shopify_df['Title'].tolist()
                fuzzy_matches = self.fuzzy_matcher.find_best_matches(
                    item_description, all_candidates, shopify_df=shopify_df
                )

            # 步驟 3: 選擇最佳匹配（不使用 AI 投票）
            if fuzzy_matches:
                best_match = fuzzy_matches[0]  # 第一個是最佳匹配

                voting_result = {
                    'final_match': best_match['matched_title'],
                    'confidence': 'HIGH' if best_match['primary_score'] >= 80 else 'MEDIUM' if best_match['primary_score'] >= 60 else 'LOW',
                    'needs_review': best_match['primary_score'] < 70,
                    'vote_count': 1,
                    'agreement_level': 'FUZZY_ONLY',
                    'model_votes': {},
                    'reasoning': f'基於模糊匹配，相似度分數: {best_match["primary_score"]:.1f}'
                }

                # 獲取匹配的 Shopify 資料
                shopify_match = shopify_df[shopify_df['Title'] == best_match['matched_title']]
                if not shopify_match.empty:
                    shopify_data = shopify_match.iloc[0].to_dict()
                else:
                    shopify_data = {}
            else:
                # 沒有找到匹配
                voting_result = {
                    'final_match': None,
                    'confidence': 'VERY_LOW',
                    'needs_review': True,
                    'vote_count': 0,
                    'agreement_level': 'NO_MATCHES',
                    'model_votes': {},
                    'reasoning': '沒有找到候選匹配項目'
                }
                shopify_data = {}

            return {
                'store_data': item_data,
                'shopify_data': shopify_data,
                'voting_result': voting_result,
                'fuzzy_matches': fuzzy_matches,
                'brand_result': brand_result,
                'final_vendor': final_vendor
            }

        except Exception as e:
            return {
                'store_data': item_data,
                'shopify_data': {},
                'voting_result': {
                    'final_match': None,
                    'confidence': 'VERY_LOW',
                    'needs_review': True,
                    'vote_count': 0,
                    'agreement_level': 'ERROR',
                    'model_votes': {},
                    'reasoning': f'處理錯誤: {str(e)}'
                },
                'fuzzy_matches': [],
                'error': str(e)
            }

    def _create_summary_report(self, results: list) -> dict:
        """創建處理摘要報告"""
        total_processed = len(results)
        successful_matches = len([r for r in results if not r['voting_result']['needs_review']])
        needs_review = len([r for r in results if r['voting_result']['needs_review']])

        return {
            'total_processed': total_processed,
            'successful_matches': successful_matches,
            'needs_review': needs_review,
            'success_rate': (successful_matches / total_processed * 100) if total_processed > 0 else 0,
            'processing_time': time.time()
        }

    def _extract_similarity_score(self, reasoning: str) -> str:
        """從推理文本中提取相似度分數"""
        import re
        match = re.search(r'相似度分數:\s*(\d+\.?\d*)', reasoning)
        if match:
            return f"{float(match.group(1)):.1f}%"
        return "N/A"
    

    
    def _render_results_tab(self):
        """渲染結果標籤頁"""
        st.header("📊 處理結果")
        
        if 'processing_results' not in st.session_state:
            st.info("ℹ️ 尚未執行處理，請先在「執行映射」標籤頁中開始處理。")
            return
        
        results_data = st.session_state['processing_results']
        results = results_data['results']
        summary = results_data['summary']
        
        # 摘要統計
        st.subheader("📈 處理摘要")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("總處理數", summary['total_processed'])
        
        with col2:
            st.metric("成功匹配", summary['successful_matches'])
        
        with col3:
            st.metric("需要審查", summary['needs_review'])
        
        with col4:
            st.metric("處理時間", f"{summary['processing_time']:.1f}s")
        
        # 結果表格
        st.subheader("📋 詳細結果")
        
        # 準備表格資料
        table_data = []
        for result in results:
            store_data = result['store_data']
            shopify_data = result['shopify_data']
            voting_result = result['voting_result']

            # 使用正確的欄位名稱
            item_desc = store_data.get('Item Description', store_data.get('item_description', ''))
            department = store_data.get('Department', store_data.get('department', ''))
            vendor = shopify_data.get('Vendor', shopify_data.get('vendor', ''))

            table_data.append({
                '產品描述': item_desc,
                '原始部門': department,
                '匹配供應商': vendor,
                '最終匹配': voting_result['final_match'] or '無匹配',
                '信心度': voting_result['confidence'],
                '需要審查': '是' if voting_result['needs_review'] else '否',
                '推理': voting_result.get('reasoning', ''),
                '相似度分數': self._extract_similarity_score(voting_result.get('reasoning', ''))
            })
        
        df_results = pd.DataFrame(table_data)
        st.dataframe(df_results, use_container_width=True)
        
        # 下載按鈕
        st.subheader("💾 下載結果")

        col1, col2, col3 = st.columns(3)

        with col1:
            # 生成完整結果 CSV
            csv_data = df_results.to_csv(index=False, encoding='utf-8-sig')
            st.download_button(
                label="📄 下載完整結果 CSV",
                data=csv_data,
                file_name=f"mapping_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv",
                use_container_width=True
            )

        with col2:
            # 生成審查清單
            review_data = df_results[df_results['需要審查'] == '是']
            if not review_data.empty:
                review_csv = review_data.to_csv(index=False, encoding='utf-8-sig')
                st.download_button(
                    label="⚠️ 下載審查清單",
                    data=review_csv,
                    file_name=f"needs_review_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv",
                    use_container_width=True
                )
            else:
                st.info("沒有需要審查的項目")

        with col3:
            # 生成統計報告
            stats_report = {
                "處理摘要": summary,
                "生成時間": datetime.now().isoformat(),
                "詳細統計": {
                    "總處理數": len(df_results),
                    "成功匹配": len(df_results[df_results['需要審查'] == '否']),
                    "需要審查": len(df_results[df_results['需要審查'] == '是']),
                    "信心度分佈": df_results['信心度'].value_counts().to_dict()
                }
            }
            stats_json = json.dumps(stats_report, ensure_ascii=False, indent=2)
            st.download_button(
                label="📊 下載統計報告",
                data=stats_json,
                file_name=f"statistics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json",
                use_container_width=True
            )


def main():
    """主函數"""
    app = ProductMappingApp()
    app.run()


if __name__ == "__main__":
    main()
