"""
測試 AI 功能是否正常運作
Test AI Functionality
"""

import os
import json
from dotenv import load_dotenv
from settings_manager import settings_manager

def test_api_keys():
    """測試 API 金鑰配置"""
    print("🔑 測試 API 金鑰配置 / Testing API Key Configuration")
    print("=" * 60)
    
    # 載入環境變數
    load_dotenv()
    
    # 檢查環境變數
    env_keys = {
        "OPENAI_API_KEY": os.getenv("OPENAI_API_KEY"),
        "ANTHROPIC_API_KEY": os.getenv("ANTHROPIC_API_KEY"),
        "GOOGLE_API_KEY": os.getenv("GOOGLE_API_KEY")
    }
    
    print("\n📝 環境變數檢查:")
    for key, value in env_keys.items():
        if value:
            masked_value = value[:8] + "..." + value[-8:] if len(value) > 16 else value
            print(f"  ✅ {key}: {masked_value}")
        else:
            print(f"  ❌ {key}: 未設定")
    
    # 檢查設定管理器中的 API 金鑰
    print("\n📝 設定管理器檢查:")
    api_keys = settings_manager.get('api_keys', {})
    for key, value in api_keys.items():
        if value:
            masked_value = value[:8] + "..." + value[-8:] if len(value) > 16 else value
            print(f"  ✅ {key}: {masked_value}")
        else:
            print(f"  ❌ {key}: 未設定")

def test_model_selection():
    """測試模型選擇"""
    print("\n🤖 測試模型選擇 / Testing Model Selection")
    print("=" * 60)
    
    selected_models = settings_manager.get_selected_models()
    active_models = [model for model, selected in selected_models.items() if selected]
    
    print(f"\n📊 模型選擇狀態:")
    for model, selected in selected_models.items():
        status = "✅ 已選中" if selected else "❌ 未選中"
        print(f"  {status} {model}")
    
    print(f"\n🎯 啟用的模型: {active_models}")
    print(f"📈 啟用模型數量: {len(active_models)}")
    
    return active_models

def test_ai_client_initialization():
    """測試 AI 客戶端初始化"""
    print("\n🔧 測試 AI 客戶端初始化 / Testing AI Client Initialization")
    print("=" * 60)
    
    try:
        # 嘗試導入 AI 相關模組
        from ai_clients.openai_client import OpenAIClient
        from ai_clients.anthropic_client import AnthropicClient
        from ai_clients.google_client import GoogleClient
        
        print("✅ AI 客戶端模組導入成功")
        
        # 測試客戶端初始化
        api_keys = settings_manager.get('api_keys', {})
        
        clients = {}
        
        # OpenAI 客戶端
        if api_keys.get('openai_api_key'):
            try:
                clients['openai'] = OpenAIClient(api_keys['openai_api_key'])
                print("✅ OpenAI 客戶端初始化成功")
            except Exception as e:
                print(f"❌ OpenAI 客戶端初始化失敗: {e}")
        
        # Anthropic 客戶端
        if api_keys.get('anthropic_api_key'):
            try:
                clients['anthropic'] = AnthropicClient(api_keys['anthropic_api_key'])
                print("✅ Anthropic 客戶端初始化成功")
            except Exception as e:
                print(f"❌ Anthropic 客戶端初始化失敗: {e}")
        
        # Google 客戶端
        if api_keys.get('google_api_key'):
            try:
                clients['google'] = GoogleClient(api_keys['google_api_key'])
                print("✅ Google 客戶端初始化成功")
            except Exception as e:
                print(f"❌ Google 客戶端初始化失敗: {e}")
        
        print(f"\n📊 成功初始化的客戶端: {list(clients.keys())}")
        return clients
        
    except ImportError as e:
        print(f"❌ AI 客戶端模組導入失敗: {e}")
        return {}

def test_simple_ai_call():
    """測試簡單的 AI 調用"""
    print("\n🧠 測試簡單的 AI 調用 / Testing Simple AI Call")
    print("=" * 60)
    
    clients = test_ai_client_initialization()
    
    test_prompt = "請回答：1+1等於多少？"
    
    for client_name, client in clients.items():
        try:
            print(f"\n🔄 測試 {client_name.upper()} 客戶端...")
            
            # 這裡需要根據實際的客戶端接口調整
            # response = client.generate_response(test_prompt)
            # print(f"✅ {client_name} 回應: {response}")
            
            print(f"✅ {client_name} 客戶端可用")
            
        except Exception as e:
            print(f"❌ {client_name} 調用失敗: {e}")

def test_processing_configuration():
    """測試處理配置"""
    print("\n⚙️ 測試處理配置 / Testing Processing Configuration")
    print("=" * 60)
    
    processing_settings = settings_manager.get_processing_settings()
    
    print("\n📋 當前處理設定:")
    for key, value in processing_settings.items():
        print(f"  {key}: {value}")
    
    # 檢查關鍵設定
    voting_threshold = processing_settings.get('voting_threshold', 2)
    infer_vendors = processing_settings.get('infer_vendors', True)
    
    print(f"\n🎯 關鍵設定檢查:")
    print(f"  投票閾值: {voting_threshold}")
    print(f"  AI 品牌推斷: {infer_vendors}")
    
    return processing_settings

def diagnose_ai_issue():
    """診斷 AI 問題"""
    print("\n🔍 診斷 AI 問題 / Diagnosing AI Issues")
    print("=" * 60)
    
    issues = []
    
    # 檢查 API 金鑰
    api_keys = settings_manager.get('api_keys', {})
    if not any(api_keys.values()):
        issues.append("❌ 沒有設定任何 API 金鑰")
    
    # 檢查模型選擇
    selected_models = settings_manager.get_selected_models()
    active_models = [model for model, selected in selected_models.items() if selected]
    if not active_models:
        issues.append("❌ 沒有選擇任何 AI 模型")
    
    # 檢查處理設定
    processing_settings = settings_manager.get_processing_settings()
    if not processing_settings.get('infer_vendors', True):
        issues.append("⚠️ AI 品牌推斷已停用")
    
    if issues:
        print("\n🚨 發現的問題:")
        for issue in issues:
            print(f"  {issue}")
    else:
        print("\n✅ 沒有發現明顯問題")
    
    return issues

def main():
    """主測試函數"""
    print("🧪 AI 功能測試")
    print("🧪 AI Functionality Test")
    print("=" * 80)
    
    try:
        # 執行所有測試
        test_api_keys()
        active_models = test_model_selection()
        test_ai_client_initialization()
        test_simple_ai_call()
        processing_settings = test_processing_configuration()
        issues = diagnose_ai_issue()
        
        print("\n" + "=" * 80)
        print("📊 測試總結 / Test Summary")
        print("=" * 80)
        
        print(f"\n✅ 啟用的 AI 模型: {len(active_models)} 個")
        for model in active_models:
            print(f"  • {model}")
        
        print(f"\n⚙️ 處理設定:")
        print(f"  • 投票閾值: {processing_settings.get('voting_threshold', 2)}")
        print(f"  • AI 品牌推斷: {processing_settings.get('infer_vendors', True)}")
        print(f"  • 批次大小: {processing_settings.get('batch_size', 10)}")
        
        if not issues:
            print(f"\n🎉 AI 功能配置正確！")
            print(f"💡 如果 AI 仍未運作，可能是:")
            print(f"  1. API 金鑰無效或額度不足")
            print(f"  2. 網路連接問題")
            print(f"  3. 處理管道中的配置問題")
        else:
            print(f"\n⚠️ 需要修復 {len(issues)} 個問題")
        
        print(f"\n🔧 建議的修復步驟:")
        print(f"  1. 確認 API 金鑰有效")
        print(f"  2. 在 Streamlit 界面中選擇 AI 模型")
        print(f"  3. 啟用 AI 品牌推斷選項")
        print(f"  4. 重新執行處理")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
