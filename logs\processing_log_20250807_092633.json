{"timestamp": "2025-08-07T09:26:33.481815", "processing_summary": {"total_processed": 5, "successful_matches": 0, "needs_review": 5, "success_rate": 0.0, "review_rate": 1.0, "confidence_distribution": {"HIGH": 0, "MEDIUM": 0, "LOW": 5, "VERY_LOW": 0}, "agreement_distribution": {"NO_CONSENSUS": 5}, "timestamp": "2025-08-07T09:26:33.436189"}, "configuration": {"ai_models": {"gpt-3.5-turbo-0125": false, "gpt4o": true, "gpt-4-turbo": false, "claude": true, "claude-3-opus": false, "gemini": true, "gemini-1.5-pro": false}, "model_pricing": {"gpt-3.5-turbo-0125": {"input": 0.5, "output": 1.5, "description": "Low-cost, fast responses"}, "gpt-4o-mini": {"input": 0.15, "output": 0.6, "description": "Balanced mid-range performance"}, "gpt-4-turbo": {"input": 10.0, "output": 30.0, "description": "Highest accuracy and capability"}, "claude-3-5-sonnet": {"input": 3.0, "output": 15.0, "description": "Low-cost, fast processing"}, "claude-3-opus": {"input": 15.0, "output": 75.0, "description": "Higher accuracy and reasoning"}, "gemini-2.0-flash-exp": {"input": 0.075, "output": 0.3, "description": "Fast, very low-cost"}, "gemini-1.5-pro": {"input": 1.25, "output": 5.0, "description": "Higher reasoning capability"}}, "ui_settings": {"language": "en", "remember_settings": true, "log_panel_width": 400}, "api_keys": {"openai_api_key": "${OPENAI_API_KEY}", "anthropic_api_key": "${ANTHROPIC_API_KEY}", "google_api_key": "${GOOGLE_API_KEY}"}, "voting": {"threshold": 2, "confidence_high_threshold": 2, "confidence_low_threshold": 1}, "vendor_from": "shopify_list_only", "prompt_path": "prompts/default_prompt.txt", "fuzzy_match": {"top_n_candidates": 5, "score_cutoff": 60}, "paths": {"store_list": "data/store_list.csv", "shopify_list": "data/shopify_list.csv", "output_dir": "output/", "logs_dir": "logs/"}, "columns": {"store_list": {"item_description": "Item Description", "department": "Department", "modified": "Modified"}, "shopify_list": {"title": "Title", "vendor": "<PERSON><PERSON><PERSON>", "product_category": "Product Category"}}, "brand_abbreviations": {"SW": "Swisse", "OST": "Ostelin", "BL": "Blackmores", "NAT": "Nature's Own", "VIT": "Vitamins"}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "ai_votes_file": "ai_votes.jsonl", "system_log_file": "system.log"}}, "detailed_log": []}