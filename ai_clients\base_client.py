"""
基礎 AI 客戶端類 - 定義所有 AI 客戶端的通用接口
Base AI Client Class - Define common interface for all AI clients
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import time
import asyncio

logger = logging.getLogger(__name__)


class BaseAIClient(ABC):
    """所有 AI 客戶端的基礎抽象類"""
    
    def __init__(self, api_key: str, model_name: str, **kwargs):
        """
        初始化 AI 客戶端
        
        Args:
            api_key: API 密鑰
            model_name: 模型名稱
            **kwargs: 其他配置參數
        """
        self.api_key = api_key
        self.model_name = model_name
        self.config = kwargs
        
        # 請求限制配置
        self.max_retries = kwargs.get('max_retries', 3)
        self.retry_delay = kwargs.get('retry_delay', 1.0)
        self.timeout = kwargs.get('timeout', 30.0)
        
        # 統計信息
        self.request_count = 0
        self.success_count = 0
        self.error_count = 0
        self.total_tokens = 0
        
        logger.info(f"初始化 {self.__class__.__name__} - 模型: {model_name}")
    
    @abstractmethod
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """
        生成 AI 回應（抽象方法）
        
        Args:
            prompt: 輸入提示
            **kwargs: 其他參數
            
        Returns:
            AI 生成的回應文本
        """
        pass
    
    @abstractmethod
    def _prepare_request(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """
        準備 API 請求（抽象方法）
        
        Args:
            prompt: 輸入提示
            **kwargs: 其他參數
            
        Returns:
            API 請求參數字典
        """
        pass
    
    @abstractmethod
    def _parse_response(self, response: Any) -> str:
        """
        解析 API 回應（抽象方法）
        
        Args:
            response: API 回應對象
            
        Returns:
            解析後的文本
        """
        pass
    
    async def generate_with_retry(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """
        帶重試機制的生成方法
        
        Args:
            prompt: 輸入提示
            **kwargs: 其他參數
            
        Returns:
            包含回應和元數據的字典
        """
        self.request_count += 1
        start_time = time.time()
        
        for attempt in range(self.max_retries):
            try:
                logger.debug(f"嘗試 {attempt + 1}/{self.max_retries} - {self.model_name}")
                
                response_text = await self.generate_response(prompt, **kwargs)
                
                # 成功統計
                self.success_count += 1
                end_time = time.time()
                
                result = {
                    'response': response_text,
                    'model': self.model_name,
                    'success': True,
                    'attempt': attempt + 1,
                    'duration': end_time - start_time,
                    'timestamp': time.time(),
                    'prompt_length': len(prompt),
                    'response_length': len(response_text)
                }
                
                logger.debug(f"成功生成回應 - {self.model_name} - 耗時: {result['duration']:.2f}s")
                return result
                
            except Exception as e:
                logger.warning(f"嘗試 {attempt + 1} 失敗 - {self.model_name}: {e}")
                
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.retry_delay * (attempt + 1))
                else:
                    # 最後一次嘗試失敗
                    self.error_count += 1
                    end_time = time.time()
                    
                    return {
                        'response': '',
                        'model': self.model_name,
                        'success': False,
                        'attempt': attempt + 1,
                        'duration': end_time - start_time,
                        'timestamp': time.time(),
                        'error': str(e),
                        'prompt_length': len(prompt),
                        'response_length': 0
                    }
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        獲取客戶端統計信息
        
        Returns:
            統計信息字典
        """
        success_rate = (self.success_count / self.request_count * 100) if self.request_count > 0 else 0
        
        return {
            'model_name': self.model_name,
            'request_count': self.request_count,
            'success_count': self.success_count,
            'error_count': self.error_count,
            'success_rate': success_rate,
            'total_tokens': self.total_tokens
        }
    
    def reset_statistics(self):
        """重置統計信息"""
        self.request_count = 0
        self.success_count = 0
        self.error_count = 0
        self.total_tokens = 0
        logger.info(f"重置 {self.model_name} 統計信息")
    
    def validate_api_key(self) -> bool:
        """
        驗證 API 密鑰是否有效
        
        Returns:
            是否有效
        """
        return bool(self.api_key and self.api_key.strip())
    
    def set_config(self, **kwargs):
        """
        更新配置參數
        
        Args:
            **kwargs: 配置參數
        """
        self.config.update(kwargs)
        logger.debug(f"更新 {self.model_name} 配置: {kwargs}")
    
    def __str__(self) -> str:
        return f"{self.__class__.__name__}(model={self.model_name})"
    
    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(model='{self.model_name}', requests={self.request_count})"
