"""
投票機制模組 - 聚合 AI 輸出並選擇最終匹配
Voting Mechanism Module - Aggregate AI outputs and select final match
"""

import logging
import yaml
from typing import Dict, List, Optional, Any, Tuple
from collections import Counter
import re

logger = logging.getLogger(__name__)


class AIVoter:
    """AI 投票機制，用於聚合多個 AI 模型的輸出並決定最終結果"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化投票器
        
        Args:
            config_path: 配置文件路徑
        """
        self.config = self._load_config(config_path)
        self.voting_config = self.config.get('voting', {})
        self.threshold = self.voting_config.get('threshold', 2)
        self.confidence_high_threshold = self.voting_config.get('confidence_high_threshold', 2)
        self.confidence_low_threshold = self.voting_config.get('confidence_low_threshold', 1)
        
        logger.info(f"投票器初始化 - 閾值: {self.threshold}")
    
    def _load_config(self, config_path: str) -> Dict:
        """載入配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            logger.error(f"配置文件未找到: {config_path}")
            raise
        except yaml.YAMLError as e:
            logger.error(f"配置文件格式錯誤: {e}")
            raise
    
    def _normalize_product_name(self, product_name: str) -> str:
        """
        標準化產品名稱以便比較
        
        Args:
            product_name: 原始產品名稱
            
        Returns:
            標準化後的產品名稱
        """
        if not product_name:
            return ""
        
        # 轉換為小寫
        normalized = product_name.lower().strip()
        
        # 移除多餘的空格
        normalized = re.sub(r'\s+', ' ', normalized)
        
        # 移除常見的標點符號
        normalized = re.sub(r'[^\w\s]', '', normalized)
        
        return normalized
    
    def _calculate_similarity(self, name1: str, name2: str) -> float:
        """
        計算兩個產品名稱的相似度
        
        Args:
            name1: 產品名稱1
            name2: 產品名稱2
            
        Returns:
            相似度分數 (0-1)
        """
        from rapidfuzz import fuzz
        
        norm1 = self._normalize_product_name(name1)
        norm2 = self._normalize_product_name(name2)
        
        if not norm1 or not norm2:
            return 0.0
        
        # 使用多種相似度算法的加權平均
        ratio = fuzz.ratio(norm1, norm2) / 100
        token_sort = fuzz.token_sort_ratio(norm1, norm2) / 100
        token_set = fuzz.token_set_ratio(norm1, norm2) / 100
        
        # 加權平均
        similarity = (ratio * 0.4 + token_sort * 0.3 + token_set * 0.3)
        return similarity
    
    def _group_similar_matches(self, matches: List[str], similarity_threshold: float = 0.85) -> Dict[str, List[str]]:
        """
        將相似的匹配結果分組
        
        Args:
            matches: 匹配結果清單
            similarity_threshold: 相似度閾值
            
        Returns:
            分組結果字典
        """
        groups = {}
        processed = set()
        
        for match in matches:
            if match in processed or not match:
                continue
            
            # 創建新組
            group_key = match
            groups[group_key] = [match]
            processed.add(match)
            
            # 找到相似的匹配
            for other_match in matches:
                if other_match in processed or not other_match:
                    continue
                
                similarity = self._calculate_similarity(match, other_match)
                if similarity >= similarity_threshold:
                    groups[group_key].append(other_match)
                    processed.add(other_match)
        
        return groups
    
    def vote_on_product_match(self, ai_results: List[Dict[str, Any]], 
                            candidates: List[str]) -> Dict[str, Any]:
        """
        對產品匹配結果進行投票
        
        Args:
            ai_results: AI 模型結果清單
            candidates: 候選產品清單
            
        Returns:
            投票結果
        """
        # 提取有效的匹配結果
        valid_matches = []
        model_votes = {}
        
        for result in ai_results:
            model_name = result.get('model', 'unknown')
            match = result.get('response', {}).get('match', '')
            reasoning = result.get('response', {}).get('reasoning', '')
            confidence = result.get('response', {}).get('confidence', 'LOW')
            success = result.get('success', False)
            
            model_votes[model_name] = {
                'match': match,
                'reasoning': reasoning,
                'confidence': confidence,
                'success': success,
                'raw_response': result.get('response', {}).get('raw_response', '')
            }
            
            # 只考慮成功的結果
            if success and match and match.strip():
                # 驗證匹配是否在候選清單中
                if self._is_valid_candidate(match, candidates):
                    valid_matches.append(match.strip())
                else:
                    logger.warning(f"{model_name} 返回的匹配 '{match}' 不在候選清單中")
        
        # 如果沒有有效匹配
        if not valid_matches:
            return {
                'final_match': None,
                'confidence': 'VERY_LOW',
                'needs_review': True,
                'vote_count': 0,
                'agreement_level': 'NO_CONSENSUS',
                'model_votes': model_votes,
                'reasoning': '沒有模型返回有效的匹配結果'
            }
        
        # 將相似的匹配分組
        match_groups = self._group_similar_matches(valid_matches)
        
        # 統計投票
        vote_counts = {}
        for group_key, group_matches in match_groups.items():
            vote_counts[group_key] = len(group_matches)
        
        # 找到得票最多的匹配
        if not vote_counts:
            return {
                'final_match': None,
                'confidence': 'VERY_LOW',
                'needs_review': True,
                'vote_count': 0,
                'agreement_level': 'NO_CONSENSUS',
                'model_votes': model_votes,
                'reasoning': '無法統計有效投票'
            }
        
        winner = max(vote_counts.items(), key=lambda x: x[1])
        winning_match, winning_votes = winner
        
        # 決定信心度和是否需要審查
        total_models = len([r for r in ai_results if r.get('success', False)])
        
        if winning_votes >= self.threshold and total_models >= 2:
            if winning_votes >= self.confidence_high_threshold:
                confidence = 'HIGH'
                needs_review = False
                agreement_level = 'STRONG_CONSENSUS'
            else:
                confidence = 'MEDIUM'
                needs_review = False
                agreement_level = 'WEAK_CONSENSUS'
        else:
            confidence = 'LOW'
            needs_review = True
            agreement_level = 'NO_CONSENSUS'
        
        # 收集推理信息
        reasoning_parts = []
        for model_name, vote_info in model_votes.items():
            if vote_info['success'] and vote_info['match']:
                reasoning_parts.append(f"{model_name}: {vote_info['reasoning']}")
        
        combined_reasoning = "; ".join(reasoning_parts) if reasoning_parts else "無可用推理信息"
        
        return {
            'final_match': winning_match,
            'confidence': confidence,
            'needs_review': needs_review,
            'vote_count': winning_votes,
            'total_models': total_models,
            'agreement_level': agreement_level,
            'model_votes': model_votes,
            'vote_distribution': vote_counts,
            'match_groups': match_groups,
            'reasoning': combined_reasoning
        }
    
    def _is_valid_candidate(self, match: str, candidates: List[str]) -> bool:
        """
        檢查匹配是否是有效的候選項目
        
        Args:
            match: 匹配結果
            candidates: 候選項目清單
            
        Returns:
            是否有效
        """
        if not match or not candidates:
            return False
        
        # 直接匹配
        if match in candidates:
            return True
        
        # 高相似度匹配
        for candidate in candidates:
            if self._calculate_similarity(match, candidate) >= 0.9:
                return True
        
        return False
    
    def vote_on_vendor_inference(self, ai_results: List[Dict[str, Any]], 
                                valid_vendors: List[str]) -> Dict[str, Any]:
        """
        對供應商推斷結果進行投票
        
        Args:
            ai_results: AI 模型結果清單
            valid_vendors: 有效供應商清單
            
        Returns:
            投票結果
        """
        valid_vendors_set = set(valid_vendors)
        vendor_votes = {}
        model_votes = {}
        
        for result in ai_results:
            model_name = result.get('model', 'unknown')
            vendor = result.get('response', {}).get('vendor', 'UNKNOWN')
            reasoning = result.get('response', {}).get('reasoning', '')
            confidence = result.get('response', {}).get('confidence', 'LOW')
            success = result.get('success', False)
            
            model_votes[model_name] = {
                'vendor': vendor,
                'reasoning': reasoning,
                'confidence': confidence,
                'success': success
            }
            
            # 只考慮成功且有效的結果
            if success and vendor != 'UNKNOWN' and vendor in valid_vendors_set:
                vendor_votes[vendor] = vendor_votes.get(vendor, 0) + 1
        
        # 決定最終供應商
        if not vendor_votes:
            return {
                'final_vendor': 'UNKNOWN',
                'confidence': 'VERY_LOW',
                'needs_review': True,
                'vote_count': 0,
                'model_votes': model_votes,
                'reasoning': '沒有模型返回有效的供應商'
            }
        
        winner = max(vendor_votes.items(), key=lambda x: x[1])
        winning_vendor, winning_votes = winner
        
        total_models = len([r for r in ai_results if r.get('success', False)])
        
        # 決定信心度
        if winning_votes >= self.threshold:
            confidence = 'HIGH' if winning_votes >= self.confidence_high_threshold else 'MEDIUM'
            needs_review = False
        else:
            confidence = 'LOW'
            needs_review = True
        
        return {
            'final_vendor': winning_vendor,
            'confidence': confidence,
            'needs_review': needs_review,
            'vote_count': winning_votes,
            'total_models': total_models,
            'model_votes': model_votes,
            'vote_distribution': vendor_votes,
            'reasoning': f"基於 {winning_votes}/{total_models} 個模型的投票結果"
        }
    
    def get_voting_statistics(self, voting_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        獲取投票統計信息
        
        Args:
            voting_results: 投票結果清單
            
        Returns:
            統計信息
        """
        if not voting_results:
            return {'total_votes': 0}
        
        total_votes = len(voting_results)
        high_confidence = len([r for r in voting_results if r.get('confidence') == 'HIGH'])
        medium_confidence = len([r for r in voting_results if r.get('confidence') == 'MEDIUM'])
        low_confidence = len([r for r in voting_results if r.get('confidence') == 'LOW'])
        needs_review = len([r for r in voting_results if r.get('needs_review', False)])
        
        agreement_levels = [r.get('agreement_level', 'UNKNOWN') for r in voting_results]
        agreement_distribution = dict(Counter(agreement_levels))
        
        return {
            'total_votes': total_votes,
            'confidence_distribution': {
                'HIGH': high_confidence,
                'MEDIUM': medium_confidence,
                'LOW': low_confidence
            },
            'needs_review_count': needs_review,
            'review_rate': needs_review / total_votes if total_votes > 0 else 0,
            'agreement_distribution': agreement_distribution
        }


if __name__ == "__main__":
    # 測試程式碼
    logging.basicConfig(level=logging.INFO)
    
    # 創建投票器
    voter = AIVoter()
    
    # 測試產品匹配投票
    test_ai_results = [
        {
            'model': 'gpt4o',
            'success': True,
            'response': {
                'match': 'Swisse Vitamin C 1000mg Tablets 60 Pack',
                'reasoning': 'Matches dosage and pack size',
                'confidence': 'HIGH'
            }
        },
        {
            'model': 'claude',
            'success': True,
            'response': {
                'match': 'Swisse Vitamin C 1000mg Tablets 60 Pack',
                'reasoning': 'Clear match based on ingredients and quantity',
                'confidence': 'HIGH'
            }
        },
        {
            'model': 'gemini',
            'success': True,
            'response': {
                'match': 'Swisse Vitamin C 500mg Tablets 120 Pack',
                'reasoning': 'Different dosage but same brand',
                'confidence': 'MEDIUM'
            }
        }
    ]
    
    test_candidates = [
        'Swisse Vitamin C 1000mg Tablets 60 Pack',
        'Swisse Vitamin C 500mg Tablets 120 Pack',
        'Swisse Multivitamin for Women 60 Tablets'
    ]
    
    print("產品匹配投票測試:")
    result = voter.vote_on_product_match(test_ai_results, test_candidates)
    print(f"最終匹配: {result['final_match']}")
    print(f"信心度: {result['confidence']}")
    print(f"需要審查: {result['needs_review']}")
    print(f"投票分佈: {result['vote_distribution']}")
    print(f"協議水平: {result['agreement_level']}")
    
    # 測試供應商推斷投票
    test_vendor_results = [
        {
            'model': 'gpt4o',
            'success': True,
            'response': {
                'vendor': 'Swisse',
                'reasoning': 'SW abbreviation clearly indicates Swisse',
                'confidence': 'HIGH'
            }
        },
        {
            'model': 'claude',
            'success': True,
            'response': {
                'vendor': 'Swisse',
                'reasoning': 'Product description suggests Swisse brand',
                'confidence': 'MEDIUM'
            }
        }
    ]
    
    test_vendors = ['Swisse', 'Blackmores', 'Ostelin', 'Nature\'s Own']
    
    print(f"\n供應商推斷投票測試:")
    vendor_result = voter.vote_on_vendor_inference(test_vendor_results, test_vendors)
    print(f"最終供應商: {vendor_result['final_vendor']}")
    print(f"信心度: {vendor_result['confidence']}")
    print(f"需要審查: {vendor_result['needs_review']}")
    print(f"投票分佈: {vendor_result['vote_distribution']}")
