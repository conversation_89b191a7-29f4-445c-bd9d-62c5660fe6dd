"""
多語言支援模組 - 英文和繁體中文
Multi-language Support Module - English and Traditional Chinese
"""

translations = {
    "en": {
        # Main UI
        "app_title": "🧠 AI-Based Product Name Mapping System",
        "app_subtitle": "Standardize pharmacy product lists to Shopify master list",
        
        # Tabs
        "tab_upload": "📤 File Upload",
        "tab_config": "🔄 Configuration", 
        "tab_process": "▶️ Execute Mapping",
        "tab_results": "📊 View Results",
        
        # File Upload
        "upload_header": "📤 Upload Data Files",
        "file_requirements": "📋 Supported File Formats and Requirements",
        "file_upload_header": "📤 Upload Data Files",
        "file_formats_info": "📋 Supported File Formats and Requirements",
        "store_list_title": "🏪 Pharmacy Product List (Store List)",
        "shopify_list_title": "🛒 Shopify Master List",
        "upload_method": "Choose import method:",
        "upload_file": "Upload File",
        "use_existing": "Use Existing File",
        "use_sample": "Use Sample Data",
        "load_selected": "Load Selected File",
        "load_sample": "Load Sample Data",
        "data_preview": "📊 Data Preview",
        "vendor_distribution": "📈 Vendor Distribution",
        
        # Configuration
        "config_header": "🔄 Processing Configuration",
        "api_settings": "🔑 API Settings",
        "model_selection": "🤖 AI Model Selection",
        "processing_options": "🔧 Processing Options",
        "cost_estimate": "💰 Cost Estimate",
        "language_setting": "🌐 Language Setting",
        
        # AI Models
        "gpt_models": "GPT (OpenAI)",
        "claude_models": "Claude (Anthropic)", 
        "gemini_models": "Gemini (Google)",
        "model_cost_input": "Input Cost",
        "model_cost_output": "Output Cost",
        "per_1m_tokens": "per 1M tokens",
        
        # Processing
        "start_processing": "🚀 Start Processing",
        "processing_status": "Processing Status",
        "step_data_loading": "Step 1/6: Data preprocessing...",
        "step_filtering": "Step 2/6: Filtering duplicates...",
        "step_brand_mapping": "Step 3/6: Brand mapping and inference...",
        "step_fuzzy_matching": "Step 4/6: Fuzzy matching...",
        "step_ai_voting": "Step 5/6: AI model voting...",
        "step_exporting": "Step 6/6: Exporting results...",
        "processing_complete": "✅ Processing completed!",
        "row_limit": "Number of Rows to Process",
        "process_all_rows": "Process All Rows",
        "batch_size": "Batch Size",
        "filter_duplicates": "Filter Duplicates",
        "ai_vendor_inference": "AI Vendor Inference",
        
        # Results
        "results_header": "📊 Processing Results",
        "processing_summary": "📈 Processing Summary",
        "total_processed": "Total Processed",
        "successful_matches": "Successful Matches",
        "needs_review": "Needs Review",
        "processing_time": "Processing Time",
        "detailed_results": "📋 Detailed Results",
        "download_results": "💾 Download Results",
        "download_csv": "📄 Download Complete Results CSV",
        "download_review": "⚠️ Download Review List",
        "download_stats": "📊 Download Statistics Report",
        
        # Sidebar
        "system_config": "🛠️ System Configuration",
        "voting_settings": "Voting Settings",
        "voting_threshold": "Voting Threshold",
        "fuzzy_settings": "Fuzzy Matching Settings",
        "candidate_count": "Number of Candidates",
        "similarity_threshold": "Similarity Threshold",
        
        # Log Panel
        "log_panel": "📝 System Logs",
        "log_level": "Log Level",
        "clear_logs": "Clear Logs",
        "export_logs": "Export Logs",
        
        # Messages
        "success_loaded": "✅ Loaded successfully!",
        "error_loading": "❌ Loading failed:",
        "warning_no_files": "⚠️ Please upload both CSV files first",
        "info_no_data": "ℹ️ No processing has been executed yet",
        "error_api_key": "❌ Please provide valid API keys",
        
        # Settings
        "remember_settings": "Remember Settings",
        "settings_saved": "Settings saved successfully",
        "settings_loaded": "Settings loaded successfully",
    },
    
    "zh-TW": {
        # Main UI
        "app_title": "🧠 AI 產品名稱映射系統",
        "app_subtitle": "將藥房產品清單標準化映射到 Shopify 主清單",
        
        # Tabs
        "tab_upload": "📤 文件上傳",
        "tab_config": "🔄 處理配置",
        "tab_process": "▶️ 執行映射",
        "tab_results": "📊 結果查看",
        
        # File Upload
        "upload_header": "📤 上傳資料文件",
        "file_requirements": "📋 支援的文件格式和要求",
        "file_upload_header": "📤 上傳資料文件",
        "file_formats_info": "📋 支援的文件格式和要求",
        "store_list_title": "🏪 藥房產品清單 (Store List)",
        "shopify_list_title": "🛒 Shopify 主清單",
        "upload_method": "選擇導入方式:",
        "upload_file": "上傳文件",
        "use_existing": "使用現有文件",
        "use_sample": "使用示例資料",
        "load_selected": "載入選中的文件",
        "load_sample": "載入示例資料",
        "data_preview": "📊 資料預覽",
        "vendor_distribution": "📈 供應商分佈",
        
        # Configuration
        "config_header": "🔄 處理配置",
        "api_settings": "🔑 API 設定",
        "model_selection": "🤖 AI 模型選擇",
        "processing_options": "🔧 處理選項",
        "cost_estimate": "💰 成本估算",
        "language_setting": "🌐 語言設定",
        
        # AI Models
        "gpt_models": "GPT (OpenAI)",
        "claude_models": "Claude (Anthropic)",
        "gemini_models": "Gemini (Google)",
        "model_cost_input": "輸入成本",
        "model_cost_output": "輸出成本",
        "per_1m_tokens": "每百萬 tokens",
        
        # Processing
        "start_processing": "🚀 開始處理",
        "processing_status": "處理狀態",
        "step_data_loading": "步驟 1/6: 資料預處理...",
        "step_filtering": "步驟 2/6: 過濾重複項目...",
        "step_brand_mapping": "步驟 3/6: 品牌映射和推斷...",
        "step_fuzzy_matching": "步驟 4/6: 模糊匹配...",
        "step_ai_voting": "步驟 5/6: AI 模型投票...",
        "step_exporting": "步驟 6/6: 匯出結果...",
        "processing_complete": "✅ 處理完成！",
        "row_limit": "處理行數限制",
        "process_all_rows": "處理所有行",
        "batch_size": "批次大小",
        "filter_duplicates": "過濾重複項目",
        "ai_vendor_inference": "AI 品牌推斷",
        
        # Results
        "results_header": "📊 處理結果",
        "processing_summary": "📈 處理摘要",
        "total_processed": "總處理數",
        "successful_matches": "成功匹配",
        "needs_review": "需要審查",
        "processing_time": "處理時間",
        "detailed_results": "📋 詳細結果",
        "download_results": "💾 下載結果",
        "download_csv": "📄 下載完整結果 CSV",
        "download_review": "⚠️ 下載審查清單",
        "download_stats": "📊 下載統計報告",
        
        # Sidebar
        "system_config": "🛠️ 系統配置",
        "voting_settings": "投票設定",
        "voting_threshold": "投票閾值",
        "fuzzy_settings": "模糊匹配設定",
        "candidate_count": "候選項目數量",
        "similarity_threshold": "相似度閾值",
        
        # Log Panel
        "log_panel": "📝 系統日誌",
        "log_level": "日誌級別",
        "clear_logs": "清除日誌",
        "export_logs": "匯出日誌",
        
        # Messages
        "success_loaded": "✅ 載入成功！",
        "error_loading": "❌ 載入失敗:",
        "warning_no_files": "⚠️ 請先上傳兩個 CSV 文件",
        "info_no_data": "ℹ️ 尚未執行處理，請先在「執行映射」標籤頁中開始處理",
        "error_api_key": "❌ 請提供有效的 API 密鑰",
        
        # Settings
        "remember_settings": "記住設定",
        "settings_saved": "設定已成功保存",
        "settings_loaded": "設定已成功載入",
    }
}


def get_text(key: str, language: str = "en") -> str:
    """
    獲取指定語言的文本
    
    Args:
        key: 文本鍵值
        language: 語言代碼 ("en" 或 "zh-TW")
        
    Returns:
        對應語言的文本
    """
    return translations.get(language, translations["en"]).get(key, key)


def get_model_info(language: str = "en") -> dict:
    """獲取 AI 模型信息"""
    if language == "zh-TW":
        return {
            "gpt-3.5-turbo-0125": "低成本，快速回應",
            "gpt-4o-mini": "平衡的中等性能",
            "gpt-4-turbo": "最高準確性和能力",
            "claude-3-5-sonnet": "低成本，快速處理",
            "claude-3-opus": "更高準確性和推理",
            "gemini-2.0-flash-exp": "快速，極低成本",
            "gemini-1.5-pro": "更高推理能力"
        }
    else:
        return {
            "gpt-3.5-turbo-0125": "Low-cost, fast responses",
            "gpt-4o-mini": "Balanced mid-range performance", 
            "gpt-4-turbo": "Highest accuracy and capability",
            "claude-3-5-sonnet": "Low-cost, fast processing",
            "claude-3-opus": "Higher accuracy and reasoning",
            "gemini-2.0-flash-exp": "Fast, very low-cost",
            "gemini-1.5-pro": "Higher reasoning capability"
        }
