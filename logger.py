"""
日誌記錄模組 - 保存結構化的 AI 輸出以便追蹤
Logger Module - Save structured AI outputs for traceability
"""

import logging
import json
import jsonlines
import yaml
from typing import Dict, List, Any, Optional
from pathlib import Path
from datetime import datetime
import os

# 設定標準日誌記錄器
logger = logging.getLogger(__name__)


class AILogger:
    """AI 日誌記錄器，專門用於記錄 AI 處理過程和結果"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化 AI 日誌記錄器
        
        Args:
            config_path: 配置文件路徑
        """
        self.config = self._load_config(config_path)
        self.logging_config = self.config.get('logging', {})
        self.logs_dir = self.config.get('paths', {}).get('logs_dir', 'logs/')
        
        # 日誌文件路徑
        ai_votes_filename = self.logging_config.get('ai_votes_file', 'ai_votes.jsonl')
        system_log_filename = self.logging_config.get('system_log_file', 'system.log')

        # 如果配置中的文件名已經包含路徑，直接使用；否則加上 logs_dir
        if os.path.dirname(ai_votes_filename):
            self.ai_votes_file = ai_votes_filename
        else:
            self.ai_votes_file = os.path.join(self.logs_dir, ai_votes_filename)

        if os.path.dirname(system_log_filename):
            self.system_log_file = system_log_filename
        else:
            self.system_log_file = os.path.join(self.logs_dir, system_log_filename)
        
        # 確保日誌目錄存在
        self._ensure_log_directory()
        
        # 設定系統日誌記錄器
        self._setup_system_logger()
        
        logger.info(f"AI 日誌記錄器初始化完成 - 日誌目錄: {self.logs_dir}")
    
    def _load_config(self, config_path: str) -> Dict:
        """載入配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            logger.error(f"配置文件未找到: {config_path}")
            raise
        except yaml.YAMLError as e:
            logger.error(f"配置文件格式錯誤: {e}")
            raise
    
    def _ensure_log_directory(self):
        """確保日誌目錄存在"""
        Path(self.logs_dir).mkdir(parents=True, exist_ok=True)
    
    def _setup_system_logger(self):
        """設定系統日誌記錄器"""
        # 創建文件處理器
        file_handler = logging.FileHandler(self.system_log_file, encoding='utf-8')
        
        # 設定日誌格式
        log_format = self.logging_config.get('format', 
                                           '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        formatter = logging.Formatter(log_format)
        file_handler.setFormatter(formatter)
        
        # 設定日誌級別
        log_level = getattr(logging, self.logging_config.get('level', 'INFO').upper())
        file_handler.setLevel(log_level)
        
        # 添加到根日誌記錄器
        root_logger = logging.getLogger()
        root_logger.addHandler(file_handler)
        root_logger.setLevel(log_level)
    
    def log_ai_vote(self, item_description: str, vendor: str, 
                   ai_results: List[Dict[str, Any]], 
                   voting_result: Dict[str, Any],
                   fuzzy_matches: Optional[List[Dict]] = None,
                   additional_data: Optional[Dict] = None):
        """
        記錄 AI 投票過程和結果
        
        Args:
            item_description: 產品描述
            vendor: 供應商
            ai_results: AI 模型結果清單
            voting_result: 投票結果
            fuzzy_matches: 模糊匹配結果（可選）
            additional_data: 額外資料（可選）
        """
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'item_description': item_description,
            'vendor': vendor,
            'ai_results': ai_results,
            'voting_result': voting_result,
            'fuzzy_matches': fuzzy_matches or [],
            'additional_data': additional_data or {}
        }
        
        # 寫入 JSONL 文件
        try:
            with jsonlines.open(self.ai_votes_file, mode='a') as writer:
                writer.write(log_entry)
            
            logger.debug(f"AI 投票日誌已記錄: {item_description}")
            
        except Exception as e:
            logger.error(f"記錄 AI 投票日誌失敗: {e}")
    
    def log_processing_step(self, step_name: str, step_data: Dict[str, Any], 
                          status: str = "SUCCESS", error_message: Optional[str] = None):
        """
        記錄處理步驟
        
        Args:
            step_name: 步驟名稱
            step_data: 步驟資料
            status: 狀態 (SUCCESS, ERROR, WARNING)
            error_message: 錯誤訊息（可選）
        """
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'step_name': step_name,
            'status': status,
            'step_data': step_data,
            'error_message': error_message
        }
        
        # 根據狀態選擇日誌級別
        if status == "ERROR":
            logger.error(f"處理步驟失敗 - {step_name}: {error_message}")
        elif status == "WARNING":
            logger.warning(f"處理步驟警告 - {step_name}: {error_message}")
        else:
            logger.info(f"處理步驟完成 - {step_name}")
        
        # 記錄詳細資料到文件
        step_log_file = os.path.join(self.logs_dir, f"processing_steps_{datetime.now().strftime('%Y%m%d')}.jsonl")
        try:
            with jsonlines.open(step_log_file, mode='a') as writer:
                writer.write(log_entry)
        except Exception as e:
            logger.error(f"記錄處理步驟失敗: {e}")
    
    def log_vendor_inference(self, item_description: str, 
                           inference_results: List[Dict[str, Any]],
                           final_vendor: str, confidence: str):
        """
        記錄供應商推斷過程
        
        Args:
            item_description: 產品描述
            inference_results: 推斷結果清單
            final_vendor: 最終供應商
            confidence: 信心度
        """
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'type': 'vendor_inference',
            'item_description': item_description,
            'inference_results': inference_results,
            'final_vendor': final_vendor,
            'confidence': confidence
        }
        
        vendor_log_file = os.path.join(self.logs_dir, f"vendor_inference_{datetime.now().strftime('%Y%m%d')}.jsonl")
        try:
            with jsonlines.open(vendor_log_file, mode='a') as writer:
                writer.write(log_entry)
            
            logger.debug(f"供應商推斷日誌已記錄: {item_description} -> {final_vendor}")
            
        except Exception as e:
            logger.error(f"記錄供應商推斷日誌失敗: {e}")
    
    def log_fuzzy_matching(self, query: str, vendor: str, 
                          matches: List[Dict[str, Any]], 
                          match_statistics: Dict[str, Any]):
        """
        記錄模糊匹配過程
        
        Args:
            query: 查詢字符串
            vendor: 供應商
            matches: 匹配結果
            match_statistics: 匹配統計
        """
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'type': 'fuzzy_matching',
            'query': query,
            'vendor': vendor,
            'matches': matches,
            'statistics': match_statistics
        }
        
        fuzzy_log_file = os.path.join(self.logs_dir, f"fuzzy_matching_{datetime.now().strftime('%Y%m%d')}.jsonl")
        try:
            with jsonlines.open(fuzzy_log_file, mode='a') as writer:
                writer.write(log_entry)
            
            logger.debug(f"模糊匹配日誌已記錄: {query} ({len(matches)} 個匹配)")
            
        except Exception as e:
            logger.error(f"記錄模糊匹配日誌失敗: {e}")
    
    def log_batch_summary(self, batch_statistics: Dict[str, Any], 
                         processing_time: float, 
                         configuration: Dict[str, Any]):
        """
        記錄批次處理摘要
        
        Args:
            batch_statistics: 批次統計
            processing_time: 處理時間
            configuration: 配置信息
        """
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'type': 'batch_summary',
            'statistics': batch_statistics,
            'processing_time_seconds': processing_time,
            'configuration': configuration
        }
        
        summary_log_file = os.path.join(self.logs_dir, f"batch_summary_{datetime.now().strftime('%Y%m%d')}.jsonl")
        try:
            with jsonlines.open(summary_log_file, mode='a') as writer:
                writer.write(log_entry)
            
            logger.info(f"批次處理摘要已記錄 - 處理時間: {processing_time:.2f}s")
            
        except Exception as e:
            logger.error(f"記錄批次摘要失敗: {e}")
    
    def read_ai_votes(self, date_filter: Optional[str] = None, 
                     item_filter: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        讀取 AI 投票日誌
        
        Args:
            date_filter: 日期過濾器 (YYYY-MM-DD)
            item_filter: 項目過濾器（產品描述關鍵字）
            
        Returns:
            日誌條目清單
        """
        if not os.path.exists(self.ai_votes_file):
            return []
        
        entries = []
        try:
            with jsonlines.open(self.ai_votes_file) as reader:
                for entry in reader:
                    # 應用過濾器
                    if date_filter and not entry.get('timestamp', '').startswith(date_filter):
                        continue
                    
                    if item_filter and item_filter.lower() not in entry.get('item_description', '').lower():
                        continue
                    
                    entries.append(entry)
            
            logger.debug(f"讀取 {len(entries)} 個 AI 投票日誌條目")
            return entries
            
        except Exception as e:
            logger.error(f"讀取 AI 投票日誌失敗: {e}")
            return []
    
    def analyze_ai_performance(self, date_filter: Optional[str] = None) -> Dict[str, Any]:
        """
        分析 AI 模型性能
        
        Args:
            date_filter: 日期過濾器
            
        Returns:
            性能分析結果
        """
        entries = self.read_ai_votes(date_filter)
        
        if not entries:
            return {'total_entries': 0}
        
        # 統計各模型的性能
        model_stats = {}
        confidence_distribution = {'HIGH': 0, 'MEDIUM': 0, 'LOW': 0, 'VERY_LOW': 0}
        agreement_levels = {}
        
        for entry in entries:
            voting_result = entry.get('voting_result', {})
            model_votes = voting_result.get('model_votes', {})
            
            # 統計信心度分佈
            confidence = voting_result.get('confidence', 'VERY_LOW')
            confidence_distribution[confidence] = confidence_distribution.get(confidence, 0) + 1
            
            # 統計協議水平
            agreement = voting_result.get('agreement_level', 'UNKNOWN')
            agreement_levels[agreement] = agreement_levels.get(agreement, 0) + 1
            
            # 統計各模型的表現
            for model_name, vote_info in model_votes.items():
                if model_name not in model_stats:
                    model_stats[model_name] = {
                        'total_votes': 0,
                        'successful_votes': 0,
                        'confidence_distribution': {'HIGH': 0, 'MEDIUM': 0, 'LOW': 0}
                    }
                
                model_stats[model_name]['total_votes'] += 1
                
                if vote_info.get('success', False):
                    model_stats[model_name]['successful_votes'] += 1
                
                vote_confidence = vote_info.get('confidence', 'LOW')
                if vote_confidence in model_stats[model_name]['confidence_distribution']:
                    model_stats[model_name]['confidence_distribution'][vote_confidence] += 1
        
        # 計算成功率
        for model_name in model_stats:
            total = model_stats[model_name]['total_votes']
            successful = model_stats[model_name]['successful_votes']
            model_stats[model_name]['success_rate'] = successful / total if total > 0 else 0
        
        return {
            'total_entries': len(entries),
            'model_statistics': model_stats,
            'confidence_distribution': confidence_distribution,
            'agreement_levels': agreement_levels,
            'analysis_timestamp': datetime.now().isoformat()
        }
    
    def cleanup_old_logs(self, days_to_keep: int = 30):
        """
        清理舊日誌文件
        
        Args:
            days_to_keep: 保留天數
        """
        import glob
        from datetime import timedelta
        
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        # 查找所有日誌文件
        log_patterns = [
            os.path.join(self.logs_dir, "processing_steps_*.jsonl"),
            os.path.join(self.logs_dir, "vendor_inference_*.jsonl"),
            os.path.join(self.logs_dir, "fuzzy_matching_*.jsonl"),
            os.path.join(self.logs_dir, "batch_summary_*.jsonl")
        ]
        
        deleted_count = 0
        for pattern in log_patterns:
            for file_path in glob.glob(pattern):
                try:
                    file_date = datetime.fromtimestamp(os.path.getmtime(file_path))
                    if file_date < cutoff_date:
                        os.remove(file_path)
                        deleted_count += 1
                        logger.info(f"刪除舊日誌文件: {file_path}")
                except Exception as e:
                    logger.error(f"刪除日誌文件失敗 {file_path}: {e}")
        
        logger.info(f"清理完成，刪除 {deleted_count} 個舊日誌文件")


if __name__ == "__main__":
    # 測試程式碼
    logging.basicConfig(level=logging.INFO)
    
    # 創建 AI 日誌記錄器
    ai_logger = AILogger()
    
    # 測試記錄 AI 投票
    test_ai_results = [
        {
            'model': 'gpt4o',
            'success': True,
            'response': {
                'match': 'Swisse Vitamin C 1000mg Tablets 60 Pack',
                'reasoning': 'Perfect match',
                'confidence': 'HIGH'
            }
        }
    ]
    
    test_voting_result = {
        'final_match': 'Swisse Vitamin C 1000mg Tablets 60 Pack',
        'confidence': 'HIGH',
        'needs_review': False,
        'vote_count': 1
    }
    
    print("測試 AI 日誌記錄...")
    ai_logger.log_ai_vote(
        item_description="SW Vit C 1000mg Tab 60",
        vendor="Swisse",
        ai_results=test_ai_results,
        voting_result=test_voting_result
    )
    
    # 測試記錄處理步驟
    ai_logger.log_processing_step(
        step_name="data_loading",
        step_data={"rows_loaded": 100, "columns": 3},
        status="SUCCESS"
    )
    
    # 測試讀取日誌
    print("\n讀取 AI 投票日誌...")
    votes = ai_logger.read_ai_votes()
    print(f"找到 {len(votes)} 個日誌條目")
    
    # 測試性能分析
    print("\n分析 AI 性能...")
    performance = ai_logger.analyze_ai_performance()
    print(f"總條目數: {performance['total_entries']}")
    print(f"模型統計: {performance.get('model_statistics', {})}")
    
    print("日誌記錄測試完成！")
