"""
測試語言切換和行數限制功能
Test Language Switching and Row Limit Functionality
"""

from translations import get_text
from settings_manager import settings_manager

def test_language_switching():
    """測試語言切換功能"""
    print("🌐 測試語言切換功能 / Testing Language Switching")
    print("=" * 60)
    
    # 測試英文
    print("\n📝 English Translations:")
    print(f"  App Title: {get_text('app_title', 'en')}")
    print(f"  Upload Header: {get_text('upload_header', 'en')}")
    print(f"  API Settings: {get_text('api_settings', 'en')}")
    print(f"  Model Selection: {get_text('model_selection', 'en')}")
    print(f"  Processing Options: {get_text('processing_options', 'en')}")
    print(f"  Row Limit: {get_text('row_limit', 'en')}")
    print(f"  Process All Rows: {get_text('process_all_rows', 'en')}")
    print(f"  Batch Size: {get_text('batch_size', 'en')}")
    print(f"  Filter Duplicates: {get_text('filter_duplicates', 'en')}")
    print(f"  AI Vendor Inference: {get_text('ai_vendor_inference', 'en')}")
    
    # 測試中文
    print("\n📝 Chinese Translations:")
    print(f"  App Title: {get_text('app_title', 'zh-TW')}")
    print(f"  Upload Header: {get_text('upload_header', 'zh-TW')}")
    print(f"  API Settings: {get_text('api_settings', 'zh-TW')}")
    print(f"  Model Selection: {get_text('model_selection', 'zh-TW')}")
    print(f"  Processing Options: {get_text('processing_options', 'zh-TW')}")
    print(f"  Row Limit: {get_text('row_limit', 'zh-TW')}")
    print(f"  Process All Rows: {get_text('process_all_rows', 'zh-TW')}")
    print(f"  Batch Size: {get_text('batch_size', 'zh-TW')}")
    print(f"  Filter Duplicates: {get_text('filter_duplicates', 'zh-TW')}")
    print(f"  AI Vendor Inference: {get_text('ai_vendor_inference', 'zh-TW')}")

def test_settings_manager():
    """測試設定管理器的新功能"""
    print("\n⚙️ 測試設定管理器 / Testing Settings Manager")
    print("=" * 60)
    
    # 測試行數限制設定
    print("\n📊 Row Limit Settings:")
    
    # 設定處理所有行
    settings_manager.set('processing_settings.process_all_rows', True)
    print(f"  Process All Rows: {settings_manager.get('processing_settings.process_all_rows')}")
    
    # 設定行數限制
    settings_manager.set('processing_settings.row_limit', 500)
    print(f"  Row Limit: {settings_manager.get('processing_settings.row_limit')}")
    
    # 測試其他處理設定
    print(f"  Voting Threshold: {settings_manager.get('processing_settings.voting_threshold')}")
    print(f"  Batch Size: {settings_manager.get('processing_settings.batch_size')}")
    print(f"  Filter Duplicates: {settings_manager.get('processing_settings.filter_duplicates')}")
    print(f"  AI Vendor Inference: {settings_manager.get('processing_settings.infer_vendors')}")
    
    # 測試語言設定
    print(f"\n🌐 Language Setting: {settings_manager.get('language')}")
    
    # 測試模型選擇
    selected_models = settings_manager.get_selected_models()
    active_models = [model for model, selected in selected_models.items() if selected]
    print(f"🤖 Active Models: {active_models}")
    
    # 測試設定摘要
    summary = settings_manager.get_settings_summary()
    print(f"\n📋 Settings Summary:")
    for key, value in summary.items():
        print(f"  {key}: {value}")

def test_row_limit_logic():
    """測試行數限制邏輯"""
    print("\n🔢 測試行數限制邏輯 / Testing Row Limit Logic")
    print("=" * 60)
    
    # 模擬不同的資料大小和設定
    test_cases = [
        {"total_rows": 1000, "process_all": True, "row_limit": 100},
        {"total_rows": 1000, "process_all": False, "row_limit": 100},
        {"total_rows": 50, "process_all": False, "row_limit": 100},
        {"total_rows": 2000, "process_all": False, "row_limit": 500},
    ]
    
    for i, case in enumerate(test_cases, 1):
        total_rows = case["total_rows"]
        process_all = case["process_all"]
        row_limit = case["row_limit"]
        
        if process_all:
            rows_to_process = total_rows
        else:
            rows_to_process = min(row_limit, total_rows)
        
        print(f"\n  Test Case {i}:")
        print(f"    Total Rows: {total_rows:,}")
        print(f"    Process All: {process_all}")
        print(f"    Row Limit: {row_limit:,}")
        print(f"    → Rows to Process: {rows_to_process:,}")
        print(f"    → Percentage: {(rows_to_process/total_rows)*100:.1f}%")

def main():
    """主測試函數"""
    print("🧪 語言切換和行數限制功能測試")
    print("🧪 Language Switching and Row Limit Feature Test")
    print("=" * 80)
    
    try:
        test_language_switching()
        test_settings_manager()
        test_row_limit_logic()
        
        print("\n✅ 所有測試完成！/ All tests completed!")
        print("\n🎯 新功能摘要 / New Features Summary:")
        print("  ✅ 完整的雙語支援 (英文/繁體中文)")
        print("  ✅ Complete bilingual support (English/Traditional Chinese)")
        print("  ✅ 行數限制選項 - 可選擇處理全部或限制數量")
        print("  ✅ Row limit option - Choose to process all or limit quantity")
        print("  ✅ 實時成本估算基於實際處理行數")
        print("  ✅ Real-time cost estimation based on actual rows to process")
        print("  ✅ 設定持久化包含所有新選項")
        print("  ✅ Settings persistence includes all new options")
        print("  ✅ 日誌系統支援雙語顯示")
        print("  ✅ Logging system supports bilingual display")
        
        print("\n🚀 使用方法 / How to Use:")
        print("  1. 在瀏覽器中打開 http://localhost:8501")
        print("  1. Open http://localhost:8501 in your browser")
        print("  2. 選擇語言 (右上角下拉選單)")
        print("  2. Select language (top-right dropdown)")
        print("  3. 在「處理配置」中設定行數限制")
        print("  3. Set row limit in 'Processing Configuration'")
        print("  4. 查看成本估算會根據行數限制更新")
        print("  4. See cost estimation updates based on row limit")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")

if __name__ == "__main__":
    main()
