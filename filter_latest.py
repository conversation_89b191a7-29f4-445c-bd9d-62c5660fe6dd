"""
重複資料過濾模組 - 根據時間戳保留最新版本
Duplicate Data Filter Module - Keep latest version based on timestamp
"""

import pandas as pd
import logging
from typing import Dict, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class LatestDataFilter:
    """根據修改時間戳過濾重複資料，保留最新版本"""
    
    def __init__(self):
        """初始化過濾器"""
        pass
    
    def filter_latest_by_description(self, df: pd.DataFrame, 
                                   description_col: str = 'item_description',
                                   timestamp_col: str = 'modified') -> pd.DataFrame:
        """
        根據產品描述分組，保留每組中修改時間最新的記錄
        
        Args:
            df: 輸入的 DataFrame
            description_col: 產品描述欄位名稱
            timestamp_col: 時間戳欄位名稱
            
        Returns:
            過濾後的 DataFrame
        """
        logger.info(f"開始過濾重複資料，原始資料行數: {len(df)}")
        
        if df.empty:
            logger.warning("輸入資料為空")
            return df
        
        # 檢查必要欄位
        if description_col not in df.columns:
            raise ValueError(f"找不到產品描述欄位: {description_col}")
        
        if timestamp_col not in df.columns:
            logger.warning(f"找不到時間戳欄位: {timestamp_col}，將使用原始順序")
            return self._filter_by_order(df, description_col)
        
        # 確保時間戳是 datetime 類型
        df = df.copy()
        df[timestamp_col] = pd.to_datetime(df[timestamp_col], errors='coerce')
        
        # 處理無效的時間戳
        invalid_timestamps = df[timestamp_col].isna()
        if invalid_timestamps.any():
            logger.warning(f"發現 {invalid_timestamps.sum()} 個無效時間戳，將使用最早時間")
            df.loc[invalid_timestamps, timestamp_col] = datetime.min
        
        # 按產品描述分組，保留每組中時間戳最新的記錄
        df_filtered = df.loc[df.groupby(description_col)[timestamp_col].idxmax()]
        
        # 重置索引
        df_filtered = df_filtered.reset_index(drop=True)
        
        duplicates_removed = len(df) - len(df_filtered)
        logger.info(f"過濾完成，移除 {duplicates_removed} 個重複項目，剩餘 {len(df_filtered)} 行")
        
        return df_filtered
    
    def _filter_by_order(self, df: pd.DataFrame, description_col: str) -> pd.DataFrame:
        """
        當沒有時間戳時，按原始順序保留最後出現的記錄
        
        Args:
            df: 輸入的 DataFrame
            description_col: 產品描述欄位名稱
            
        Returns:
            過濾後的 DataFrame
        """
        # 保留每個產品描述的最後一次出現
        df_filtered = df.drop_duplicates(subset=[description_col], keep='last')
        df_filtered = df_filtered.reset_index(drop=True)
        
        duplicates_removed = len(df) - len(df_filtered)
        logger.info(f"按順序過濾完成，移除 {duplicates_removed} 個重複項目")
        
        return df_filtered
    
    def get_duplicate_statistics(self, df: pd.DataFrame, 
                               description_col: str = 'item_description',
                               timestamp_col: str = 'modified') -> Dict:
        """
        獲取重複資料統計信息
        
        Args:
            df: 輸入的 DataFrame
            description_col: 產品描述欄位名稱
            timestamp_col: 時間戳欄位名稱
            
        Returns:
            統計信息字典
        """
        if df.empty:
            return {'total_rows': 0, 'unique_descriptions': 0, 'duplicates': 0}
        
        total_rows = len(df)
        unique_descriptions = df[description_col].nunique()
        duplicates = total_rows - unique_descriptions
        
        # 找出重複最多的產品
        duplicate_counts = df[description_col].value_counts()
        most_duplicated = duplicate_counts.head(5).to_dict()
        
        stats = {
            'total_rows': total_rows,
            'unique_descriptions': unique_descriptions,
            'duplicates': duplicates,
            'duplicate_rate': duplicates / total_rows if total_rows > 0 else 0,
            'most_duplicated_items': most_duplicated
        }
        
        if timestamp_col in df.columns:
            # 時間範圍統計
            df_temp = df.copy()
            df_temp[timestamp_col] = pd.to_datetime(df_temp[timestamp_col], errors='coerce')
            valid_timestamps = df_temp[timestamp_col].dropna()
            
            if not valid_timestamps.empty:
                stats['timestamp_range'] = {
                    'earliest': valid_timestamps.min().isoformat(),
                    'latest': valid_timestamps.max().isoformat(),
                    'valid_timestamps': len(valid_timestamps),
                    'invalid_timestamps': len(df) - len(valid_timestamps)
                }
        
        logger.info(f"重複資料統計: {stats}")
        return stats
    
    def preview_duplicates(self, df: pd.DataFrame, 
                          description_col: str = 'item_description',
                          timestamp_col: str = 'modified',
                          limit: int = 10) -> pd.DataFrame:
        """
        預覽重複的產品項目
        
        Args:
            df: 輸入的 DataFrame
            description_col: 產品描述欄位名稱
            timestamp_col: 時間戳欄位名稱
            limit: 顯示的重複項目數量限制
            
        Returns:
            包含重複項目的 DataFrame
        """
        if df.empty:
            return pd.DataFrame()
        
        # 找出有重複的產品描述
        duplicate_descriptions = df[description_col].value_counts()
        duplicate_descriptions = duplicate_descriptions[duplicate_descriptions > 1]
        
        if duplicate_descriptions.empty:
            logger.info("沒有發現重複的產品項目")
            return pd.DataFrame()
        
        # 取前 N 個重複最多的項目
        top_duplicates = duplicate_descriptions.head(limit).index.tolist()
        
        # 篩選出這些重複項目的所有記錄
        duplicate_rows = df[df[description_col].isin(top_duplicates)]
        
        # 按產品描述和時間戳排序
        if timestamp_col in df.columns:
            duplicate_rows = duplicate_rows.sort_values([description_col, timestamp_col])
        else:
            duplicate_rows = duplicate_rows.sort_values([description_col])
        
        logger.info(f"預覽 {len(top_duplicates)} 個重複項目，共 {len(duplicate_rows)} 行記錄")
        return duplicate_rows
    
    def filter_and_report(self, df: pd.DataFrame, 
                         description_col: str = 'item_description',
                         timestamp_col: str = 'modified') -> tuple[pd.DataFrame, Dict]:
        """
        過濾重複資料並生成詳細報告
        
        Args:
            df: 輸入的 DataFrame
            description_col: 產品描述欄位名稱
            timestamp_col: 時間戳欄位名稱
            
        Returns:
            (過濾後的 DataFrame, 詳細報告)
        """
        # 獲取過濾前統計
        before_stats = self.get_duplicate_statistics(df, description_col, timestamp_col)
        
        # 執行過濾
        filtered_df = self.filter_latest_by_description(df, description_col, timestamp_col)
        
        # 獲取過濾後統計
        after_stats = self.get_duplicate_statistics(filtered_df, description_col, timestamp_col)
        
        # 生成報告
        report = {
            'before_filtering': before_stats,
            'after_filtering': after_stats,
            'removed_rows': before_stats['total_rows'] - after_stats['total_rows'],
            'removal_rate': (before_stats['total_rows'] - after_stats['total_rows']) / before_stats['total_rows'] if before_stats['total_rows'] > 0 else 0
        }
        
        return filtered_df, report


if __name__ == "__main__":
    # 測試程式碼
    logging.basicConfig(level=logging.INFO)
    
    # 創建測試資料
    test_data = pd.DataFrame({
        'item_description': [
            'Vitamin C 1000mg',
            'Vitamin C 1000mg',
            'Vitamin D 2000IU',
            'Vitamin C 1000mg',
            'Fish Oil 1000mg'
        ],
        'department': ['Vitamins', 'Vitamins', 'Vitamins', 'Vitamins', 'Supplements'],
        'modified': [
            '2024-01-01 10:00:00',
            '2024-01-02 11:00:00',
            '2024-01-01 12:00:00',
            '2024-01-03 09:00:00',  # 最新的 Vitamin C
            '2024-01-01 14:00:00'
        ]
    })
    
    filter_obj = LatestDataFilter()
    
    print("原始測試資料:")
    print(test_data)
    print("\n" + "="*50 + "\n")
    
    # 獲取統計信息
    stats = filter_obj.get_duplicate_statistics(test_data)
    print("重複資料統計:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    print("\n" + "="*50 + "\n")
    
    # 預覽重複項目
    duplicates = filter_obj.preview_duplicates(test_data)
    print("重複項目預覽:")
    print(duplicates)
    
    print("\n" + "="*50 + "\n")
    
    # 執行過濾
    filtered_data, report = filter_obj.filter_and_report(test_data)
    print("過濾後資料:")
    print(filtered_data)
    
    print("\n過濾報告:")
    print(f"移除行數: {report['removed_rows']}")
    print(f"移除比例: {report['removal_rate']:.2%}")
