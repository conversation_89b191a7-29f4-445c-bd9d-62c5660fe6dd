"""
主程式 - AI 產品名稱映射系統
Main Program - AI Product Name Mapping System
"""

import asyncio
import logging
import time
import argparse
from typing import Dict, List, Any, Optional
from pathlib import Path

# 導入所有模組
from loader import DataLoader
from filter_latest import LatestDataFilter
from brand_map import BrandMapper
from vendor_infer import VendorInferenceEngine
from fuzzy_match import FuzzyMatcher
from prompt_engine import PromptEngine
from ai_clients.client_manager import AIClientManager
from voter import AIVoter
from exporter import ResultExporter
from logger import AILogger


class ProductMappingPipeline:
    """產品映射處理管道"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化處理管道
        
        Args:
            config_path: 配置文件路徑
        """
        self.config_path = config_path
        
        # 初始化所有組件
        self.data_loader = DataLoader(config_path)
        self.filter_engine = LatestDataFilter()
        self.brand_mapper = BrandMapper(config_path)
        self.vendor_inferrer = VendorInferenceEngine(config_path)
        self.fuzzy_matcher = FuzzyMatcher(config_path)
        self.prompt_engine = PromptEngine(config_path)
        self.ai_manager = AIClientManager(config_path)
        self.voter = AIVoter(config_path)
        self.exporter = ResultExporter(config_path)
        self.ai_logger = AILogger(config_path)
        
        # 設定日誌
        self._setup_logging()
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("產品映射管道初始化完成")
    
    def _setup_logging(self):
        """設定日誌記錄"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/main.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    
    async def process_single_item(self, item_data: Dict[str, Any], 
                                shopify_df, valid_vendors: List[str]) -> Dict[str, Any]:
        """
        處理單個產品項目
        
        Args:
            item_data: 產品項目資料
            shopify_df: Shopify 資料框
            valid_vendors: 有效供應商清單
            
        Returns:
            處理結果
        """
        item_description = item_data.get('item_description', '')
        department = item_data.get('department', '')
        
        self.logger.debug(f"處理項目: {item_description}")
        
        try:
            # 步驟 1: 品牌處理
            brand_result = self.brand_mapper.process_brand(department)
            
            # 步驟 2: 如果品牌無效，使用 AI 推斷
            if not brand_result['is_valid']:
                self.logger.debug(f"品牌無效，使用 AI 推斷: {item_description}")
                vendor_inference = await self.vendor_inferrer.infer_vendor_multi_model(item_description)
                final_vendor = vendor_inference['final_vendor']
                
                # 記錄供應商推斷
                self.ai_logger.log_vendor_inference(
                    item_description, 
                    vendor_inference.get('ai_results', []),
                    final_vendor,
                    vendor_inference.get('confidence', 'LOW')
                )
            else:
                final_vendor = brand_result['final_vendor']
            
            # 步驟 3: 模糊匹配
            if final_vendor and final_vendor in valid_vendors:
                fuzzy_matches = self.fuzzy_matcher.match_product_by_vendor(
                    item_description, final_vendor, shopify_df
                )
            else:
                # 在所有產品中搜索
                all_candidates = shopify_df['title'].tolist()
                fuzzy_matches = self.fuzzy_matcher.find_best_matches(
                    item_description, all_candidates, shopify_df=shopify_df
                )
            
            # 記錄模糊匹配
            match_stats = self.fuzzy_matcher.get_match_statistics(fuzzy_matches)
            self.ai_logger.log_fuzzy_matching(
                item_description, final_vendor or 'ALL', fuzzy_matches, match_stats
            )
            
            # 步驟 4: 如果有匹配結果，進行 AI 投票或使用最佳模糊匹配
            if fuzzy_matches:
                available_clients = self.ai_manager.get_available_clients()

                if available_clients:
                    # 有 AI 客戶端可用，進行 AI 投票
                    # 準備 AI 提示
                    prompt_info = self.prompt_engine.create_product_matching_prompt(
                        item_description, fuzzy_matches, final_vendor
                    )

                    # 獲取 AI 回應
                    ai_results = await self.ai_manager.generate_responses_parallel(
                        prompt_info['prompt']
                    )

                    # 解析 AI 回應
                    parsed_results = []
                    for model_name, result in ai_results.items():
                        if result['success']:
                            parsed_response = self.prompt_engine.parse_ai_response(
                                result['response'], 'product_match'
                            )
                            parsed_results.append({
                                'model': model_name,
                                'success': True,
                                'response': parsed_response
                            })
                        else:
                            parsed_results.append({
                                'model': model_name,
                                'success': False,
                                'response': {'match': '', 'reasoning': result.get('error', ''), 'confidence': 'LOW'}
                            })

                    # AI 投票
                    candidates = [match['matched_title'] for match in fuzzy_matches]
                    voting_result = self.voter.vote_on_product_match(parsed_results, candidates)

                    # 記錄 AI 投票
                    self.ai_logger.log_ai_vote(
                        item_description, final_vendor or 'UNKNOWN',
                        parsed_results, voting_result, fuzzy_matches
                    )
                else:
                    # 沒有 AI 客戶端，使用最佳模糊匹配結果
                    self.logger.info("沒有可用的 AI 客戶端，使用最佳模糊匹配結果")
                    best_match = fuzzy_matches[0]  # 第一個是最佳匹配

                    voting_result = {
                        'final_match': best_match['matched_title'],
                        'confidence': 'MEDIUM' if best_match['primary_score'] >= 80 else 'LOW',
                        'needs_review': best_match['primary_score'] < 70,
                        'vote_count': 1,
                        'agreement_level': 'FUZZY_ONLY',
                        'model_votes': {},
                        'reasoning': f'基於模糊匹配，相似度分數: {best_match["primary_score"]:.1f}'
                    }

                # 獲取匹配的 Shopify 資料
                if voting_result['final_match']:
                    shopify_match = shopify_df[shopify_df['title'] == voting_result['final_match']]
                    if not shopify_match.empty:
                        shopify_data = shopify_match.iloc[0].to_dict()
                    else:
                        shopify_data = {}
                else:
                    shopify_data = {}
            
            else:
                # 沒有找到匹配
                voting_result = {
                    'final_match': None,
                    'confidence': 'VERY_LOW',
                    'needs_review': True,
                    'vote_count': 0,
                    'agreement_level': 'NO_MATCHES',
                    'model_votes': {},
                    'reasoning': '沒有找到候選匹配項目'
                }
                shopify_data = {}
            
            return {
                'store_data': item_data,
                'shopify_data': shopify_data,
                'voting_result': voting_result,
                'fuzzy_matches': fuzzy_matches,
                'brand_result': brand_result,
                'final_vendor': final_vendor
            }
            
        except Exception as e:
            self.logger.error(f"處理項目失敗 {item_description}: {e}")
            return {
                'store_data': item_data,
                'shopify_data': {},
                'voting_result': {
                    'final_match': None,
                    'confidence': 'VERY_LOW',
                    'needs_review': True,
                    'vote_count': 0,
                    'agreement_level': 'ERROR',
                    'model_votes': {},
                    'reasoning': f'處理錯誤: {str(e)}'
                },
                'fuzzy_matches': [],
                'error': str(e)
            }
    
    async def process_batch(self, store_file: str, shopify_file: str, 
                          batch_size: int = 10) -> Dict[str, Any]:
        """
        批次處理產品映射
        
        Args:
            store_file: 藥房清單文件路徑
            shopify_file: Shopify 清單文件路徑
            batch_size: 批次大小
            
        Returns:
            處理結果
        """
        start_time = time.time()
        
        try:
            # 載入資料
            self.logger.info("載入資料文件...")
            store_df = self.data_loader.load_store_list(store_file)
            shopify_df = self.data_loader.load_shopify_list(shopify_file)
            
            # 設定有效供應商
            valid_vendors = self.data_loader.get_unique_vendors(shopify_df)
            self.brand_mapper.set_valid_vendors(valid_vendors)
            self.vendor_inferrer.set_valid_vendors(valid_vendors)
            self.vendor_inferrer.set_ai_clients(self.ai_manager.clients)
            
            # 過濾重複項目
            self.logger.info("過濾重複項目...")
            filtered_df, filter_report = self.filter_engine.filter_and_report(store_df)
            
            # 記錄處理步驟
            self.ai_logger.log_processing_step(
                "data_loading",
                {
                    "store_rows": len(store_df),
                    "shopify_rows": len(shopify_df),
                    "valid_vendors": len(valid_vendors),
                    "filtered_rows": len(filtered_df),
                    "duplicates_removed": filter_report['removed_rows']
                }
            )
            
            # 批次處理
            results = []
            total_items = len(filtered_df)
            
            for i in range(0, total_items, batch_size):
                batch_end = min(i + batch_size, total_items)
                batch_df = filtered_df.iloc[i:batch_end]
                
                self.logger.info(f"處理批次 {i//batch_size + 1}: {i+1}-{batch_end}/{total_items}")
                
                # 處理批次中的每個項目
                batch_tasks = []
                for _, row in batch_df.iterrows():
                    item_data = row.to_dict()
                    task = self.process_single_item(item_data, shopify_df, valid_vendors)
                    batch_tasks.append(task)
                
                # 並行處理批次
                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
                
                # 處理結果
                for result in batch_results:
                    if isinstance(result, Exception):
                        self.logger.error(f"批次處理錯誤: {result}")
                    else:
                        results.append(result)
            
            # 計算統計信息
            processing_time = time.time() - start_time
            summary = self.exporter.create_summary_report(results)
            
            # 記錄批次摘要
            self.ai_logger.log_batch_summary(
                summary, processing_time, 
                {
                    'batch_size': batch_size,
                    'total_items': total_items,
                    'ai_models': list(self.ai_manager.clients.keys())
                }
            )
            
            self.logger.info(f"處理完成！總時間: {processing_time:.2f}s")
            
            return {
                'results': results,
                'summary': summary,
                'processing_time': processing_time,
                'filter_report': filter_report
            }
            
        except Exception as e:
            self.logger.error(f"批次處理失敗: {e}")
            raise


async def main():
    """主函數"""
    parser = argparse.ArgumentParser(description='AI 產品名稱映射系統')
    parser.add_argument('--store-file', required=True, help='藥房產品清單 CSV 文件路徑')
    parser.add_argument('--shopify-file', required=True, help='Shopify 主清單 CSV 文件路徑')
    parser.add_argument('--config', default='config.yaml', help='配置文件路徑')
    parser.add_argument('--batch-size', type=int, default=10, help='批次處理大小')
    parser.add_argument('--output-dir', help='輸出目錄（可選）')
    
    args = parser.parse_args()
    
    # 檢查文件是否存在
    if not Path(args.store_file).exists():
        print(f"錯誤: 藥房清單文件不存在: {args.store_file}")
        return
    
    if not Path(args.shopify_file).exists():
        print(f"錯誤: Shopify 清單文件不存在: {args.shopify_file}")
        return
    
    try:
        # 創建處理管道
        pipeline = ProductMappingPipeline(args.config)
        
        print("🚀 開始處理產品映射...")
        
        # 執行批次處理
        processing_data = await pipeline.process_batch(
            args.store_file, 
            args.shopify_file, 
            args.batch_size
        )
        
        # 匯出結果
        print("📤 匯出結果...")
        output_files = pipeline.exporter.export_all_outputs(
            processing_data['results'], 
            processing_data
        )
        
        # 顯示結果摘要
        summary = processing_data['summary']
        print(f"\n✅ 處理完成！")
        print(f"📊 處理摘要:")
        print(f"   總處理數: {summary['total_processed']}")
        print(f"   成功匹配: {summary['successful_matches']}")
        print(f"   需要審查: {summary['needs_review']}")
        print(f"   成功率: {summary['success_rate']:.2%}")
        print(f"   處理時間: {processing_data['processing_time']:.2f}s")
        
        print(f"\n📁 輸出文件:")
        for file_type, file_path in output_files.items():
            print(f"   {file_type}: {file_path}")
        
    except Exception as e:
        print(f"❌ 處理失敗: {e}")
        logging.error(f"主程式執行失敗: {e}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(main())
