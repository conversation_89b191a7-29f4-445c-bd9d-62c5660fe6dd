"""
完整系統測試 - 測試所有功能
Full System Test - Test all functionality
"""

import asyncio
import logging
import os
from dotenv import load_dotenv

# 載入環境變數
load_dotenv()

# 導入所有模組
from main import ProductMappingPipeline
from settings_manager import settings_manager
from translations import get_text

async def test_system():
    """測試完整系統"""
    print("🧠 AI 產品名稱映射系統 - 完整功能測試")
    print("=" * 60)
    
    # 檢查 API 密鑰
    print("🔑 檢查 API 密鑰...")
    openai_key = os.getenv('OPENAI_API_KEY')
    anthropic_key = os.getenv('ANTHROPIC_API_KEY')
    google_key = os.getenv('GOOGLE_API_KEY')
    
    print(f"  OpenAI: {'✅ 已設定' if openai_key else '❌ 未設定'}")
    print(f"  Anthropic: {'✅ 已設定' if anthropic_key else '❌ 未設定'}")
    print(f"  Google: {'✅ 已設定' if google_key else '❌ 未設定'}")
    
    # 測試設定管理器
    print("\n⚙️ 測試設定管理器...")
    
    # 設定一些測試值
    settings_manager.set('language', 'zh-TW')
    settings_manager.set('selected_models.gpt-4o-mini', True)
    settings_manager.set('selected_models.claude-3-5-sonnet', True)
    
    print(f"  語言設定: {settings_manager.get('language')}")
    print(f"  選中的模型: {[k for k, v in settings_manager.get_selected_models().items() if v]}")
    
    # 測試多語言
    print("\n🌐 測試多語言支援...")
    print(f"  英文: {get_text('app_title', 'en')}")
    print(f"  中文: {get_text('app_title', 'zh-TW')}")
    
    # 測試處理管道
    print("\n🔄 測試處理管道...")
    try:
        pipeline = ProductMappingPipeline()
        print("  ✅ 處理管道初始化成功")
        
        # 測試 AI 客戶端
        available_clients = pipeline.ai_manager.get_available_clients()
        print(f"  可用的 AI 客戶端: {available_clients}")
        
        if available_clients:
            print("\n🤖 測試 AI 連接...")
            connection_results = await pipeline.ai_manager.test_all_connections()
            for model, result in connection_results.items():
                status = "✅ 成功" if result['success'] else f"❌ 失敗: {result.get('error', 'Unknown')}"
                print(f"    {model}: {status}")
        
    except Exception as e:
        print(f"  ❌ 處理管道初始化失敗: {e}")
    
    # 測試小批量處理
    print("\n📊 測試小批量處理...")
    try:
        store_file = "data/store_list_sample.csv"
        shopify_file = "data/shopify_list_sample.csv"
        
        if os.path.exists(store_file) and os.path.exists(shopify_file):
            print(f"  使用示例資料進行測試...")
            
            # 只處理前2個項目
            processing_data = await pipeline.process_batch(
                store_file, shopify_file, batch_size=2
            )
            
            summary = processing_data['summary']
            print(f"  ✅ 處理完成!")
            print(f"    總處理數: {summary['total_processed']}")
            print(f"    成功匹配: {summary['successful_matches']}")
            print(f"    需要審查: {summary['needs_review']}")
            print(f"    成功率: {summary['success_rate']:.2%}")
            print(f"    處理時間: {processing_data['processing_time']:.2f}s")
            
        else:
            print(f"  ⚠️ 示例資料文件不存在，跳過處理測試")
            
    except Exception as e:
        print(f"  ❌ 處理測試失敗: {e}")
    
    print("\n🎉 系統測試完成!")
    print("\n📋 功能清單:")
    print("  ✅ 多語言支援 (英文/繁體中文)")
    print("  ✅ 設定管理和持久化")
    print("  ✅ API 密鑰管理")
    print("  ✅ AI 模型選擇和成本估算")
    print("  ✅ 文件導入 (CSV/Excel)")
    print("  ✅ 模糊匹配和 AI 投票")
    print("  ✅ 結果匯出和日誌記錄")
    print("  ✅ Streamlit GUI 界面")
    
    print("\n🚀 您可以:")
    print("  1. 在瀏覽器中打開 http://localhost:8501 使用 GUI")
    print("  2. 運行 'python main.py --store-file data/store_list_real.csv --shopify-file data/shopify_list_real.csv' 處理實際資料")
    print("  3. 在 GUI 中配置 API 密鑰和模型選擇")
    print("  4. 上傳您的資料文件並執行映射")


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_system())
