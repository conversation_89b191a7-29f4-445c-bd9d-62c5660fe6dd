"""
模糊匹配模組 - 使用 rapidfuzz 進行產品名稱匹配
Fuzzy Matching Module - Use rapidfuzz for product name matching
"""

import logging
import yaml
from typing import Dict, List, Tuple, Optional
import pandas as pd
from rapidfuzz import fuzz, process
import re

logger = logging.getLogger(__name__)


class FuzzyMatcher:
    """使用 rapidfuzz 進行產品名稱模糊匹配的引擎"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化模糊匹配器
        
        Args:
            config_path: 配置文件路徑
        """
        self.config = self._load_config(config_path)
        self.fuzzy_config = self.config.get('fuzzy_match', {})
        self.top_n = self.fuzzy_config.get('top_n_candidates', 5)
        self.score_cutoff = self.fuzzy_config.get('score_cutoff', 60)
        
        logger.info(f"模糊匹配器初始化 - Top N: {self.top_n}, 分數閾值: {self.score_cutoff}")
    
    def _load_config(self, config_path: str) -> Dict:
        """載入配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            logger.error(f"配置文件未找到: {config_path}")
            raise
        except yaml.YAMLError as e:
            logger.error(f"配置文件格式錯誤: {e}")
            raise

    def _get_column_name(self, df: pd.DataFrame, possible_names: List[str]) -> Optional[str]:
        """獲取實際存在的欄位名稱"""
        for name in possible_names:
            if name in df.columns:
                return name
        return None

    def _preprocess_text(self, text: str) -> str:
        """
        預處理文本以提高匹配準確性
        
        Args:
            text: 原始文本
            
        Returns:
            預處理後的文本
        """
        if not text:
            return ""
        
        # 轉換為小寫
        text = text.lower()
        
        # 移除常見的非關鍵詞
        stop_words = ['tablet', 'capsule', 'pack', 'bottle', 'mg', 'iu', 'ml', 'g']
        for word in stop_words:
            text = re.sub(rf'\b{word}s?\b', '', text)
        
        # 標準化空格
        text = re.sub(r'\s+', ' ', text).strip()
        
        # 移除特殊字符（保留字母、數字、空格）
        text = re.sub(r'[^a-z0-9\s]', ' ', text)
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def _calculate_similarity_scores(self, query: str, target: str) -> Dict[str, float]:
        """
        計算多種相似度分數
        
        Args:
            query: 查詢字符串
            target: 目標字符串
            
        Returns:
            相似度分數字典
        """
        scores = {
            'ratio': fuzz.ratio(query, target),
            'partial_ratio': fuzz.partial_ratio(query, target),
            'token_sort_ratio': fuzz.token_sort_ratio(query, target),
            'token_set_ratio': fuzz.token_set_ratio(query, target),
            'WRatio': fuzz.WRatio(query, target)
        }
        
        # 計算加權平均分數
        weights = {
            'ratio': 0.2,
            'partial_ratio': 0.15,
            'token_sort_ratio': 0.25,
            'token_set_ratio': 0.25,
            'WRatio': 0.15
        }
        
        weighted_score = sum(scores[key] * weights[key] for key in scores)
        scores['weighted_average'] = weighted_score
        
        return scores
    
    def find_best_matches(self, query: str, candidates: List[str], 
                         vendor_filter: Optional[str] = None,
                         shopify_df: Optional[pd.DataFrame] = None) -> List[Dict]:
        """
        找到最佳匹配的候選項目
        
        Args:
            query: 查詢的產品名稱
            candidates: 候選產品名稱清單
            vendor_filter: 供應商過濾器
            shopify_df: Shopify 資料框（用於供應商過濾）
            
        Returns:
            匹配結果清單
        """
        if not query or not candidates:
            return []
        
        # 如果提供了供應商過濾器和 Shopify 資料框，先過濾候選項目
        filtered_candidates = candidates
        if vendor_filter and shopify_df is not None:
            # 嘗試不同的可能欄位名稱
            vendor_col = self._get_column_name(shopify_df, ['Vendor', 'vendor', 'VENDOR'])
            title_col = self._get_column_name(shopify_df, ['Title', 'title', 'TITLE'])

            if vendor_col and title_col:
                vendor_mask = shopify_df[vendor_col] == vendor_filter
                filtered_candidates = shopify_df[vendor_mask][title_col].tolist()
                logger.debug(f"按供應商 '{vendor_filter}' 過濾，候選項目從 {len(candidates)} 減少到 {len(filtered_candidates)}")
            else:
                logger.warning(f"找不到供應商或標題欄位。可用欄位: {list(shopify_df.columns)}")
        
        if not filtered_candidates:
            logger.warning(f"供應商 '{vendor_filter}' 沒有找到任何產品")
            return []
        
        # 預處理查詢字符串
        processed_query = self._preprocess_text(query)
        
        # 使用 rapidfuzz 進行匹配
        matches = process.extract(
            processed_query,
            [self._preprocess_text(candidate) for candidate in filtered_candidates],
            scorer=fuzz.WRatio,
            limit=self.top_n,
            score_cutoff=self.score_cutoff
        )
        
        # 構建詳細結果
        results = []
        for match_text, score, index in matches:
            original_candidate = filtered_candidates[index]
            
            # 計算詳細的相似度分數
            detailed_scores = self._calculate_similarity_scores(processed_query, match_text)
            
            # 獲取對應的 Shopify 資料
            shopify_info = {}
            if shopify_df is not None:
                title_col = self._get_column_name(shopify_df, ['Title', 'title', 'TITLE'])
                vendor_col = self._get_column_name(shopify_df, ['Vendor', 'vendor', 'VENDOR'])
                category_col = self._get_column_name(shopify_df, ['Product Category', 'product_category', 'PRODUCT_CATEGORY'])

                if title_col:
                    matching_rows = shopify_df[shopify_df[title_col] == original_candidate]
                    if not matching_rows.empty:
                        row = matching_rows.iloc[0]
                        shopify_info = {
                            'vendor': row.get(vendor_col, '') if vendor_col else '',
                            'product_category': row.get(category_col, '') if category_col else '',
                            'title': row.get(title_col, '') if title_col else ''
                        }
            
            result = {
                'original_query': query,
                'processed_query': processed_query,
                'matched_title': original_candidate,
                'processed_match': match_text,
                'primary_score': score,
                'detailed_scores': detailed_scores,
                'rank': len(results) + 1,
                'shopify_info': shopify_info
            }
            
            results.append(result)
        
        logger.debug(f"找到 {len(results)} 個匹配項目，查詢: '{query}'")
        return results
    
    def match_product_by_vendor(self, query: str, vendor: str, 
                               shopify_df: pd.DataFrame) -> List[Dict]:
        """
        在指定供應商範圍內匹配產品
        
        Args:
            query: 查詢的產品名稱
            vendor: 指定的供應商
            shopify_df: Shopify 資料框
            
        Returns:
            匹配結果清單
        """
        if shopify_df.empty:
            return []
        
        # 過濾指定供應商的產品
        vendor_col = self._get_column_name(shopify_df, ['Vendor', 'vendor', 'VENDOR'])
        title_col = self._get_column_name(shopify_df, ['Title', 'title', 'TITLE'])

        if not vendor_col or not title_col:
            logger.error(f"找不到供應商或標題欄位。可用欄位: {list(shopify_df.columns)}")
            return []

        vendor_products = shopify_df[shopify_df[vendor_col] == vendor]

        if vendor_products.empty:
            logger.warning(f"供應商 '{vendor}' 沒有找到任何產品")
            return []

        candidates = vendor_products[title_col].tolist()
        
        # 執行模糊匹配
        matches = self.find_best_matches(
            query=query,
            candidates=candidates,
            vendor_filter=vendor,
            shopify_df=shopify_df
        )
        
        return matches
    
    def batch_match(self, queries: List[str], shopify_df: pd.DataFrame,
                   vendor_mapping: Optional[Dict[str, str]] = None) -> List[Dict]:
        """
        批量匹配產品
        
        Args:
            queries: 查詢清單
            shopify_df: Shopify 資料框
            vendor_mapping: 查詢到供應商的映射（可選）
            
        Returns:
            批量匹配結果
        """
        results = []
        
        for i, query in enumerate(queries):
            logger.debug(f"處理查詢 {i+1}/{len(queries)}: {query}")
            
            # 確定供應商過濾器
            vendor_filter = None
            if vendor_mapping and query in vendor_mapping:
                vendor_filter = vendor_mapping[query]
            
            # 執行匹配
            if vendor_filter:
                matches = self.match_product_by_vendor(query, vendor_filter, shopify_df)
            else:
                # 在所有產品中搜索
                title_col = self._get_column_name(shopify_df, ['Title', 'title', 'TITLE'])
                if title_col:
                    all_candidates = shopify_df[title_col].tolist()
                    matches = self.find_best_matches(
                        query=query,
                        candidates=all_candidates,
                        shopify_df=shopify_df
                    )
                else:
                    logger.error(f"找不到標題欄位。可用欄位: {list(shopify_df.columns)}")
                    matches = []
            
            # 添加查詢索引
            for match in matches:
                match['query_index'] = i
                match['vendor_filter'] = vendor_filter
            
            results.extend(matches)
        
        logger.info(f"批量匹配完成，處理 {len(queries)} 個查詢，生成 {len(results)} 個匹配結果")
        return results
    
    def get_match_statistics(self, matches: List[Dict]) -> Dict[str, any]:
        """
        獲取匹配統計信息
        
        Args:
            matches: 匹配結果清單
            
        Returns:
            統計信息字典
        """
        if not matches:
            return {'total_matches': 0}
        
        scores = [match['primary_score'] for match in matches]
        
        stats = {
            'total_matches': len(matches),
            'average_score': sum(scores) / len(scores),
            'max_score': max(scores),
            'min_score': min(scores),
            'high_quality_matches': len([s for s in scores if s >= 80]),
            'medium_quality_matches': len([s for s in scores if 60 <= s < 80]),
            'low_quality_matches': len([s for s in scores if s < 60]),
        }
        
        # 按供應商統計
        vendor_stats = {}
        for match in matches:
            vendor = match.get('shopify_info', {}).get('vendor', 'Unknown')
            if vendor not in vendor_stats:
                vendor_stats[vendor] = {'count': 0, 'avg_score': 0, 'scores': []}
            vendor_stats[vendor]['count'] += 1
            vendor_stats[vendor]['scores'].append(match['primary_score'])
        
        # 計算每個供應商的平均分數
        for vendor in vendor_stats:
            scores = vendor_stats[vendor]['scores']
            vendor_stats[vendor]['avg_score'] = sum(scores) / len(scores)
            del vendor_stats[vendor]['scores']  # 移除原始分數清單
        
        stats['vendor_statistics'] = vendor_stats
        
        return stats


if __name__ == "__main__":
    # 測試程式碼
    logging.basicConfig(level=logging.INFO)
    
    # 創建測試資料
    test_shopify_data = pd.DataFrame({
        'title': [
            'Swisse Vitamin C 1000mg Tablets 60 Pack',
            'Swisse Multivitamin for Women 60 Tablets',
            'Blackmores Fish Oil 1000mg 200 Capsules',
            'Ostelin Vitamin D 1000IU Drops 20ml',
            'Nature\'s Own Calcium Magnesium 200 Tablets',
            'Vitamins Plus Iron Complex 100 Tablets'
        ],
        'vendor': [
            'Swisse', 'Swisse', 'Blackmores', 
            'Ostelin', 'Nature\'s Own', 'Vitamins'
        ],
        'product_category': [
            'Vitamins', 'Vitamins', 'Supplements',
            'Vitamins', 'Minerals', 'Vitamins'
        ]
    })
    
    # 創建模糊匹配器
    matcher = FuzzyMatcher()
    
    # 測試查詢
    test_queries = [
        'SW Vit C 1000mg Tab 60',
        'BL Fish Oil 1000mg Cap',
        'Ostelin Vit D Drops',
        'Swisse Women Multi',
        'Unknown Product Name'
    ]
    
    print("模糊匹配測試結果:")
    print("=" * 80)
    
    for query in test_queries:
        print(f"\n查詢: '{query}'")
        
        # 在所有產品中搜索
        matches = matcher.find_best_matches(
            query=query,
            candidates=test_shopify_data['title'].tolist(),
            shopify_df=test_shopify_data
        )
        
        if matches:
            print(f"找到 {len(matches)} 個匹配:")
            for i, match in enumerate(matches[:3], 1):  # 只顯示前3個
                print(f"  {i}. {match['matched_title']}")
                print(f"     分數: {match['primary_score']:.1f}")
                print(f"     供應商: {match['shopify_info'].get('vendor', 'N/A')}")
        else:
            print("  沒有找到匹配項目")
    
    # 測試批量匹配
    print(f"\n\n批量匹配測試:")
    batch_results = matcher.batch_match(test_queries, test_shopify_data)
    stats = matcher.get_match_statistics(batch_results)
    
    print(f"總匹配數: {stats['total_matches']}")
    print(f"平均分數: {stats['average_score']:.1f}")
    print(f"高質量匹配: {stats['high_quality_matches']}")
    print(f"供應商統計: {stats['vendor_statistics']}")
