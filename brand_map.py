"""
品牌映射模組 - 處理品牌縮寫擴展和驗證
Brand Mapping Module - Handle brand abbreviation expansion and validation
"""

import yaml
import logging
from typing import Dict, List, Optional, Set
import re
from difflib import get_close_matches

logger = logging.getLogger(__name__)


class BrandMapper:
    """品牌映射器，處理縮寫擴展和品牌驗證"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化品牌映射器
        
        Args:
            config_path: 配置文件路徑
        """
        self.config = self._load_config(config_path)
        self.abbreviation_map = self.config.get('brand_abbreviations', {})
        self.valid_vendors = set()  # 將由 Shopify 清單設定
        
        # 建立反向映射（完整名稱到縮寫）
        self.reverse_map = {v.lower(): k for k, v in self.abbreviation_map.items()}
        
        logger.info(f"載入 {len(self.abbreviation_map)} 個品牌縮寫映射")
    
    def _load_config(self, config_path: str) -> Dict:
        """載入配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            logger.error(f"配置文件未找到: {config_path}")
            raise
        except yaml.YAMLError as e:
            logger.error(f"配置文件格式錯誤: {e}")
            raise
    
    def set_valid_vendors(self, vendors: List[str]):
        """
        設定有效的供應商清單（來自 Shopify 清單）
        
        Args:
            vendors: 有效供應商名稱清單
        """
        self.valid_vendors = set(vendor.strip() for vendor in vendors if vendor.strip())
        logger.info(f"設定 {len(self.valid_vendors)} 個有效供應商")
    
    def expand_abbreviation(self, brand_input: str) -> Optional[str]:
        """
        擴展品牌縮寫
        
        Args:
            brand_input: 輸入的品牌名稱（可能是縮寫）
            
        Returns:
            擴展後的品牌名稱，如果沒有找到映射則返回 None
        """
        if not brand_input or not brand_input.strip():
            return None
        
        brand_clean = brand_input.strip().upper()
        
        # 直接查找縮寫映射
        if brand_clean in self.abbreviation_map:
            expanded = self.abbreviation_map[brand_clean]
            logger.debug(f"品牌縮寫擴展: {brand_input} -> {expanded}")
            return expanded
        
        return None
    
    def validate_vendor(self, vendor_name: str) -> bool:
        """
        驗證供應商是否在有效清單中
        
        Args:
            vendor_name: 供應商名稱
            
        Returns:
            是否為有效供應商
        """
        if not vendor_name or not vendor_name.strip():
            return False
        
        return vendor_name.strip() in self.valid_vendors
    
    def find_closest_vendor(self, vendor_input: str, max_suggestions: int = 3) -> List[str]:
        """
        找到最接近的有效供應商名稱
        
        Args:
            vendor_input: 輸入的供應商名稱
            max_suggestions: 最大建議數量
            
        Returns:
            最接近的供應商名稱清單
        """
        if not vendor_input or not vendor_input.strip():
            return []
        
        if not self.valid_vendors:
            logger.warning("尚未設定有效供應商清單")
            return []
        
        # 使用 difflib 找到最接近的匹配
        close_matches = get_close_matches(
            vendor_input.strip(),
            list(self.valid_vendors),
            n=max_suggestions,
            cutoff=0.6  # 60% 相似度閾值
        )
        
        if close_matches:
            logger.debug(f"找到接近的供應商: {vendor_input} -> {close_matches}")
        
        return close_matches
    
    def process_brand(self, brand_input: str) -> Dict[str, any]:
        """
        處理品牌名稱的完整流程
        
        Args:
            brand_input: 輸入的品牌名稱
            
        Returns:
            處理結果字典
        """
        result = {
            'original': brand_input,
            'expanded': None,
            'final_vendor': None,
            'is_valid': False,
            'suggestions': [],
            'processing_steps': []
        }
        
        if not brand_input or not brand_input.strip():
            result['processing_steps'].append("輸入為空")
            return result
        
        brand_clean = brand_input.strip()
        result['processing_steps'].append(f"清理輸入: '{brand_clean}'")
        
        # 步驟 1: 嘗試縮寫擴展
        expanded = self.expand_abbreviation(brand_clean)
        if expanded:
            result['expanded'] = expanded
            result['processing_steps'].append(f"縮寫擴展: {brand_clean} -> {expanded}")
            brand_to_validate = expanded
        else:
            result['processing_steps'].append("未找到縮寫映射")
            brand_to_validate = brand_clean
        
        # 步驟 2: 驗證是否為有效供應商
        if self.validate_vendor(brand_to_validate):
            result['final_vendor'] = brand_to_validate
            result['is_valid'] = True
            result['processing_steps'].append(f"驗證成功: '{brand_to_validate}' 是有效供應商")
        else:
            result['processing_steps'].append(f"驗證失敗: '{brand_to_validate}' 不是有效供應商")
            
            # 步驟 3: 尋找相似的供應商
            suggestions = self.find_closest_vendor(brand_to_validate)
            result['suggestions'] = suggestions
            if suggestions:
                result['processing_steps'].append(f"找到相似供應商: {suggestions}")
            else:
                result['processing_steps'].append("未找到相似供應商")
        
        return result
    
    def add_abbreviation(self, abbreviation: str, full_name: str):
        """
        動態添加新的縮寫映射
        
        Args:
            abbreviation: 縮寫
            full_name: 完整名稱
        """
        self.abbreviation_map[abbreviation.upper()] = full_name
        self.reverse_map[full_name.lower()] = abbreviation.upper()
        logger.info(f"添加新的品牌映射: {abbreviation} -> {full_name}")
    
    def get_statistics(self) -> Dict[str, any]:
        """
        獲取品牌映射統計信息
        
        Returns:
            統計信息字典
        """
        return {
            'total_abbreviations': len(self.abbreviation_map),
            'valid_vendors_count': len(self.valid_vendors),
            'abbreviation_list': list(self.abbreviation_map.keys()),
            'vendor_list': sorted(list(self.valid_vendors))
        }
    
    def export_mappings(self, file_path: str):
        """
        匯出當前的品牌映射到文件
        
        Args:
            file_path: 輸出文件路徑
        """
        export_data = {
            'brand_abbreviations': self.abbreviation_map,
            'valid_vendors': sorted(list(self.valid_vendors)),
            'export_timestamp': pd.Timestamp.now().isoformat()
        }
        
        with open(file_path, 'w', encoding='utf-8') as file:
            yaml.dump(export_data, file, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"品牌映射已匯出到: {file_path}")


if __name__ == "__main__":
    # 測試程式碼
    import pandas as pd
    
    logging.basicConfig(level=logging.INFO)
    
    # 創建測試的品牌映射器
    mapper = BrandMapper()
    
    # 設定測試的有效供應商
    test_vendors = [
        "Swisse", "Ostelin", "Blackmores", "Nature's Own", 
        "Vitamins", "Fish Oil Co", "Healthy Life"
    ]
    mapper.set_valid_vendors(test_vendors)
    
    # 測試品牌處理
    test_brands = [
        "SW",           # 應該擴展為 Swisse
        "OST",          # 應該擴展為 Ostelin
        "Blackmores",   # 已經是有效供應商
        "BL",           # 應該擴展為 Blackmores
        "Unknown",      # 未知品牌
        "Swis",         # 拼寫錯誤，應該建議 Swisse
        "",             # 空輸入
        "NAT"           # 應該擴展為 Nature's Own
    ]
    
    print("品牌處理測試結果:")
    print("=" * 80)
    
    for brand in test_brands:
        result = mapper.process_brand(brand)
        print(f"\n輸入: '{brand}'")
        print(f"  擴展後: {result['expanded']}")
        print(f"  最終供應商: {result['final_vendor']}")
        print(f"  是否有效: {result['is_valid']}")
        print(f"  建議: {result['suggestions']}")
        print(f"  處理步驟: {' -> '.join(result['processing_steps'])}")
    
    # 顯示統計信息
    stats = mapper.get_statistics()
    print(f"\n\n統計信息:")
    print(f"  縮寫映射數量: {stats['total_abbreviations']}")
    print(f"  有效供應商數量: {stats['valid_vendors_count']}")
    print(f"  縮寫清單: {stats['abbreviation_list']}")
