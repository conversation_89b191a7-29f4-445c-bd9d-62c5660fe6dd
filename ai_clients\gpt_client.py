"""
GPT 客戶端 - OpenAI GPT-4o mini 整合
GPT Client - OpenAI GPT-4o mini integration
"""

import logging
from typing import Dict, Any
import openai
from .base_client import BaseAIClient

logger = logging.getLogger(__name__)


class GPTClient(BaseAIClient):
    """OpenAI GPT 客戶端"""
    
    def __init__(self, api_key: str, model_name: str = "gpt-4o-mini", **kwargs):
        """
        初始化 GPT 客戶端
        
        Args:
            api_key: OpenAI API 密鑰
            model_name: GPT 模型名稱
            **kwargs: 其他配置參數
        """
        super().__init__(api_key, model_name, **kwargs)
        
        # 設定 OpenAI 客戶端
        self.client = openai.AsyncOpenAI(api_key=api_key)
        
        # GPT 特定配置
        self.temperature = kwargs.get('temperature', 0.1)
        self.max_tokens = kwargs.get('max_tokens', 1000)
        self.top_p = kwargs.get('top_p', 1.0)
        self.frequency_penalty = kwargs.get('frequency_penalty', 0.0)
        self.presence_penalty = kwargs.get('presence_penalty', 0.0)
        
        logger.info(f"GPT 客戶端初始化完成 - 模型: {model_name}")
    
    def _prepare_request(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """
        準備 GPT API 請求參數
        
        Args:
            prompt: 輸入提示
            **kwargs: 其他參數
            
        Returns:
            API 請求參數字典
        """
        # 合併配置參數
        request_params = {
            'model': self.model_name,
            'messages': [
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'temperature': kwargs.get('temperature', self.temperature),
            'max_tokens': kwargs.get('max_tokens', self.max_tokens),
            'top_p': kwargs.get('top_p', self.top_p),
            'frequency_penalty': kwargs.get('frequency_penalty', self.frequency_penalty),
            'presence_penalty': kwargs.get('presence_penalty', self.presence_penalty),
        }
        
        return request_params
    
    def _parse_response(self, response: Any) -> str:
        """
        解析 GPT API 回應
        
        Args:
            response: OpenAI API 回應對象
            
        Returns:
            解析後的文本
        """
        try:
            content = response.choices[0].message.content
            
            # 更新 token 統計
            if hasattr(response, 'usage'):
                self.total_tokens += response.usage.total_tokens
                logger.debug(f"GPT Token 使用: {response.usage.total_tokens}")
            
            return content.strip() if content else ""
            
        except (AttributeError, IndexError, KeyError) as e:
            logger.error(f"解析 GPT 回應失敗: {e}")
            return ""
    
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """
        生成 GPT 回應
        
        Args:
            prompt: 輸入提示
            **kwargs: 其他參數
            
        Returns:
            AI 生成的回應文本
        """
        if not self.validate_api_key():
            raise ValueError("無效的 OpenAI API 密鑰")
        
        # 準備請求參數
        request_params = self._prepare_request(prompt, **kwargs)
        
        try:
            # 調用 OpenAI API
            response = await self.client.chat.completions.create(**request_params)
            
            # 解析回應
            response_text = self._parse_response(response)
            
            if not response_text:
                raise ValueError("GPT 回應為空")
            
            return response_text
            
        except openai.RateLimitError as e:
            logger.error(f"GPT API 速率限制: {e}")
            raise Exception(f"API 速率限制: {e}")
        
        except openai.AuthenticationError as e:
            logger.error(f"GPT API 認證失敗: {e}")
            raise Exception(f"API 認證失敗: {e}")
        
        except openai.APIError as e:
            logger.error(f"GPT API 錯誤: {e}")
            raise Exception(f"API 錯誤: {e}")
        
        except Exception as e:
            logger.error(f"GPT 請求失敗: {e}")
            raise Exception(f"請求失敗: {e}")
    
    def estimate_tokens(self, text: str) -> int:
        """
        估算文本的 token 數量
        
        Args:
            text: 輸入文本
            
        Returns:
            估算的 token 數量
        """
        # 簡單估算：英文約 4 字符 = 1 token，中文約 1.5 字符 = 1 token
        english_chars = len([c for c in text if ord(c) < 128])
        chinese_chars = len(text) - english_chars
        
        estimated_tokens = (english_chars / 4) + (chinese_chars / 1.5)
        return int(estimated_tokens)
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        獲取模型信息
        
        Returns:
            模型信息字典
        """
        return {
            'provider': 'OpenAI',
            'model': self.model_name,
            'type': 'Chat Completion',
            'max_context_length': 128000,  # GPT-4o mini 的上下文長度
            'supports_streaming': True,
            'supports_function_calling': True
        }
    
    async def test_connection(self) -> Dict[str, Any]:
        """
        測試 API 連接
        
        Returns:
            測試結果
        """
        test_prompt = "Hello, this is a connection test. Please respond with 'Connection successful.'"
        
        try:
            result = await self.generate_with_retry(test_prompt)
            
            return {
                'success': result['success'],
                'model': self.model_name,
                'response_time': result['duration'],
                'error': result.get('error', None)
            }
            
        except Exception as e:
            return {
                'success': False,
                'model': self.model_name,
                'error': str(e)
            }


if __name__ == "__main__":
    # 測試程式碼
    import asyncio
    import os
    from dotenv import load_dotenv

    load_dotenv()

    async def test_gpt_client():
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            print("請設定 OPENAI_API_KEY 環境變數")
            return

        client = GPTClient(api_key)

        # 測試連接
        print("測試 GPT 連接...")
        test_result = await client.test_connection()
        print(f"連接測試結果: {test_result}")

        if test_result['success']:
            # 測試產品匹配
            test_prompt = """You are a pharmacy product data expert.

Given an internal product name and standard names, identify the best match.

Internal name: "SW Vit C 1000mg Tab 60"

Standard names:
1. Swisse Vitamin C 1000mg Tablets 60 Pack
2. Swisse Vitamin C 500mg Tablets 120 Pack

Format response as:
MATCH: [exact product name]
REASONING: [brief explanation]"""

            print("\n測試產品匹配...")
            result = await client.generate_with_retry(test_prompt)
            print(f"回應: {result['response']}")
            print(f"耗時: {result['duration']:.2f}s")

            # 顯示統計信息
            stats = client.get_statistics()
            print(f"\n統計信息: {stats}")

    # 運行測試
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_gpt_client())
