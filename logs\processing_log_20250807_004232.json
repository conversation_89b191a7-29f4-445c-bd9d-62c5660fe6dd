{"timestamp": "2025-08-07T00:42:32.246050", "processing_summary": {"total_processed": 5, "successful_matches": 5, "needs_review": 0, "success_rate": 1.0, "review_rate": 0.0, "confidence_distribution": {"HIGH": 0, "MEDIUM": 5, "LOW": 0, "VERY_LOW": 0}, "agreement_distribution": {"FUZZY_ONLY": 5}, "timestamp": "2025-08-07T00:42:32.236115"}, "configuration": {"ai_models": {"gpt4o": false, "claude": false, "gemini": false}, "api_keys": {"openai_api_key": "${OPENAI_API_KEY}", "anthropic_api_key": "${ANTHROPIC_API_KEY}", "google_api_key": "${GOOGLE_API_KEY}"}, "voting": {"threshold": 2, "confidence_high_threshold": 2, "confidence_low_threshold": 1}, "vendor_from": "shopify_list_only", "prompt_path": "prompts/default_prompt.txt", "fuzzy_match": {"top_n_candidates": 5, "score_cutoff": 60}, "paths": {"store_list": "data/store_list.csv", "shopify_list": "data/shopify_list.csv", "output_dir": "output/", "logs_dir": "logs/"}, "columns": {"store_list": {"item_description": "Item Description", "department": "Department", "modified": "Modified"}, "shopify_list": {"title": "Title", "vendor": "<PERSON><PERSON><PERSON>", "product_category": "Product Category"}}, "brand_abbreviations": {"SW": "Swisse", "OST": "Ostelin", "BL": "Blackmores", "NAT": "Nature's Own", "VIT": "Vitamins"}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "ai_votes_file": "ai_votes.jsonl", "system_log_file": "system.log"}}, "detailed_log": []}