"""
提示引擎模組 - 處理 AI 提示模板渲染和候選項目處理
Prompt Engine Module - Handle AI prompt template rendering and candidate processing
"""

import logging
import yaml
from typing import Dict, List, Optional, Any
from pathlib import Path
import re

logger = logging.getLogger(__name__)


class PromptEngine:
    """AI 提示引擎，負責生成和管理 AI 模型的提示"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化提示引擎
        
        Args:
            config_path: 配置文件路徑
        """
        self.config = self._load_config(config_path)
        self.prompt_path = self.config.get('prompt_path', 'prompts/default_prompt.txt')
        self.default_template = self._load_prompt_template()
        
        logger.info(f"提示引擎初始化，載入模板: {self.prompt_path}")
    
    def _load_config(self, config_path: str) -> Dict:
        """載入配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            logger.error(f"配置文件未找到: {config_path}")
            raise
        except yaml.YAMLError as e:
            logger.error(f"配置文件格式錯誤: {e}")
            raise
    
    def _load_prompt_template(self) -> str:
        """載入提示模板"""
        try:
            with open(self.prompt_path, 'r', encoding='utf-8') as file:
                template = file.read()
            logger.debug(f"載入提示模板成功，長度: {len(template)} 字符")
            return template
        except FileNotFoundError:
            logger.warning(f"提示模板文件未找到: {self.prompt_path}，使用預設模板")
            return self._get_default_template()
        except Exception as e:
            logger.error(f"載入提示模板失敗: {e}")
            return self._get_default_template()
    
    def _get_default_template(self) -> str:
        """獲取預設提示模板"""
        return """You are a pharmacy product data expert.

Given an internal (possibly incorrect) product name, and a list of standard product names under the same vendor (brand), identify which standard name is the best match.

Internal name:
"{item_description}"

Standard name list:
{standard_name_1}
{standard_name_2}
{standard_name_3}
{standard_name_4}
{standard_name_5}

Instructions:
- Choose only one best match from the list.
- Base your decision on ingredients, volume, dosage form, and naming style.
- Return:
1. The best matching product name.
2. Short reasoning (1-2 sentences).

Format your response as:
MATCH: [exact product name from the list]
REASONING: [your reasoning]"""
    
    def prepare_candidates(self, fuzzy_matches: List[Dict], max_candidates: int = 5) -> List[str]:
        """
        準備候選項目清單
        
        Args:
            fuzzy_matches: 模糊匹配結果
            max_candidates: 最大候選項目數量
            
        Returns:
            候選項目清單
        """
        if not fuzzy_matches:
            return []
        
        # 取前 N 個最佳匹配
        candidates = []
        for match in fuzzy_matches[:max_candidates]:
            title = match.get('matched_title', '')
            if title and title not in candidates:
                candidates.append(title)
        
        # 如果候選項目不足，用空字符串填充
        while len(candidates) < max_candidates:
            candidates.append("")
        
        logger.debug(f"準備 {len([c for c in candidates if c])} 個有效候選項目")
        return candidates[:max_candidates]
    
    def render_prompt(self, item_description: str, candidates: List[str], 
                     template: Optional[str] = None) -> str:
        """
        渲染提示模板
        
        Args:
            item_description: 產品描述
            candidates: 候選項目清單
            template: 自定義模板（可選）
            
        Returns:
            渲染後的提示
        """
        if template is None:
            template = self.default_template
        
        # 準備模板變數
        template_vars = {
            'item_description': item_description or ""
        }
        
        # 添加候選項目變數
        for i in range(5):  # 支持最多 5 個候選項目
            key = f'standard_name_{i+1}'
            if i < len(candidates) and candidates[i]:
                template_vars[key] = f"{i+1}. {candidates[i]}"
            else:
                template_vars[key] = f"{i+1}. [No candidate]"
        
        try:
            # 渲染模板
            rendered_prompt = template.format(**template_vars)
            logger.debug(f"提示渲染成功，長度: {len(rendered_prompt)} 字符")
            return rendered_prompt
        except KeyError as e:
            logger.error(f"模板變數缺失: {e}")
            raise ValueError(f"提示模板中缺少變數: {e}")
        except Exception as e:
            logger.error(f"提示渲染失敗: {e}")
            raise
    
    def create_product_matching_prompt(self, item_description: str, 
                                     fuzzy_matches: List[Dict],
                                     vendor: Optional[str] = None) -> Dict[str, Any]:
        """
        創建產品匹配提示
        
        Args:
            item_description: 產品描述
            fuzzy_matches: 模糊匹配結果
            vendor: 供應商名稱（可選）
            
        Returns:
            提示信息字典
        """
        # 準備候選項目
        candidates = self.prepare_candidates(fuzzy_matches)
        
        # 渲染提示
        prompt = self.render_prompt(item_description, candidates)
        
        # 構建提示信息
        prompt_info = {
            'prompt': prompt,
            'item_description': item_description,
            'vendor': vendor,
            'candidates': candidates,
            'fuzzy_matches': fuzzy_matches,
            'candidate_count': len([c for c in candidates if c]),
            'template_used': 'default'
        }
        
        return prompt_info
    
    def create_vendor_inference_prompt(self, item_description: str, 
                                     valid_vendors: List[str]) -> str:
        """
        創建供應商推斷提示
        
        Args:
            item_description: 產品描述
            valid_vendors: 有效供應商清單
            
        Returns:
            供應商推斷提示
        """
        vendor_list = '\n'.join(f"- {vendor}" for vendor in sorted(valid_vendors))
        
        template = """You are a pharmacy product expert. Given a product description, identify the most likely brand/vendor from the provided list.

Product Description: "{item_description}"

Valid Vendors:
{vendor_list}

Instructions:
- Analyze the product description for brand indicators (brand names, product lines, packaging style)
- Choose ONLY from the provided vendor list
- If no clear brand can be identified, return "UNKNOWN"
- Provide reasoning for your choice

Format your response as:
VENDOR: [exact vendor name from list or UNKNOWN]
REASONING: [brief explanation]
CONFIDENCE: [HIGH/MEDIUM/LOW]"""
        
        return template.format(
            item_description=item_description,
            vendor_list=vendor_list
        )
    
    def parse_ai_response(self, response_text: str, response_type: str = 'product_match') -> Dict[str, Any]:
        """
        解析 AI 回應
        
        Args:
            response_text: AI 回應文本
            response_type: 回應類型 ('product_match' 或 'vendor_inference')
            
        Returns:
            解析結果字典
        """
        if response_type == 'product_match':
            return self._parse_product_match_response(response_text)
        elif response_type == 'vendor_inference':
            return self._parse_vendor_inference_response(response_text)
        else:
            raise ValueError(f"不支持的回應類型: {response_type}")
    
    def _parse_product_match_response(self, response_text: str) -> Dict[str, Any]:
        """解析產品匹配回應"""
        result = {
            'match': None,
            'reasoning': '',
            'confidence': 'LOW',
            'raw_response': response_text,
            'parse_success': False
        }
        
        if not response_text:
            return result
        
        lines = response_text.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            
            # 解析匹配結果
            if line.startswith('MATCH:'):
                match_text = line.replace('MATCH:', '').strip()
                # 移除可能的編號前綴
                match_text = re.sub(r'^\d+\.\s*', '', match_text)
                result['match'] = match_text
                result['parse_success'] = True
            
            # 解析推理
            elif line.startswith('REASONING:'):
                result['reasoning'] = line.replace('REASONING:', '').strip()
            
            # 解析信心度（如果有）
            elif line.startswith('CONFIDENCE:'):
                confidence = line.replace('CONFIDENCE:', '').strip().upper()
                if confidence in ['HIGH', 'MEDIUM', 'LOW']:
                    result['confidence'] = confidence
        
        # 如果沒有找到標準格式，嘗試從文本中提取
        if not result['parse_success']:
            result = self._fallback_parse_product_match(response_text)
        
        return result
    
    def _parse_vendor_inference_response(self, response_text: str) -> Dict[str, Any]:
        """解析供應商推斷回應"""
        result = {
            'vendor': 'UNKNOWN',
            'reasoning': '',
            'confidence': 'LOW',
            'raw_response': response_text,
            'parse_success': False
        }
        
        if not response_text:
            return result
        
        lines = response_text.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            
            if line.startswith('VENDOR:'):
                vendor = line.replace('VENDOR:', '').strip()
                result['vendor'] = vendor
                result['parse_success'] = True
            
            elif line.startswith('REASONING:'):
                result['reasoning'] = line.replace('REASONING:', '').strip()
            
            elif line.startswith('CONFIDENCE:'):
                confidence = line.replace('CONFIDENCE:', '').strip().upper()
                if confidence in ['HIGH', 'MEDIUM', 'LOW']:
                    result['confidence'] = confidence
        
        return result
    
    def _fallback_parse_product_match(self, response_text: str) -> Dict[str, Any]:
        """備用解析方法，當標準格式解析失敗時使用"""
        result = {
            'match': None,
            'reasoning': response_text,
            'confidence': 'LOW',
            'raw_response': response_text,
            'parse_success': False
        }
        
        # 嘗試找到產品名稱模式
        lines = response_text.split('\n')
        for line in lines:
            line = line.strip()
            # 查找包含常見產品詞彙的行
            if any(word in line.lower() for word in ['tablet', 'capsule', 'vitamin', 'mg', 'iu']):
                # 移除可能的前綴
                clean_line = re.sub(r'^\d+\.\s*', '', line)
                clean_line = re.sub(r'^[^\w]*', '', clean_line)
                if clean_line:
                    result['match'] = clean_line
                    result['parse_success'] = True
                    break
        
        return result
    
    def validate_prompt_template(self, template: str) -> Dict[str, Any]:
        """
        驗證提示模板的有效性
        
        Args:
            template: 提示模板
            
        Returns:
            驗證結果
        """
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'required_variables': []
        }
        
        # 檢查必需的變數
        required_vars = ['item_description', 'standard_name_1', 'standard_name_2', 
                        'standard_name_3', 'standard_name_4', 'standard_name_5']
        
        for var in required_vars:
            if f'{{{var}}}' not in template:
                validation_result['errors'].append(f"缺少必需變數: {{{var}}}")
                validation_result['is_valid'] = False
            else:
                validation_result['required_variables'].append(var)
        
        # 檢查模板長度
        if len(template) < 100:
            validation_result['warnings'].append("模板可能過短")
        elif len(template) > 2000:
            validation_result['warnings'].append("模板可能過長")
        
        # 檢查是否包含指導說明
        if 'instructions' not in template.lower():
            validation_result['warnings'].append("模板可能缺少明確的指導說明")
        
        return validation_result


if __name__ == "__main__":
    # 測試程式碼
    logging.basicConfig(level=logging.INFO)
    
    # 創建提示引擎
    engine = PromptEngine()
    
    # 測試候選項目準備
    test_fuzzy_matches = [
        {'matched_title': 'Swisse Vitamin C 1000mg Tablets 60 Pack', 'primary_score': 95},
        {'matched_title': 'Swisse Vitamin C 500mg Tablets 120 Pack', 'primary_score': 85},
        {'matched_title': 'Blackmores Vitamin C 1000mg Tablets', 'primary_score': 75},
    ]
    
    candidates = engine.prepare_candidates(test_fuzzy_matches)
    print("準備的候選項目:")
    for i, candidate in enumerate(candidates, 1):
        print(f"  {i}. {candidate}")
    
    # 測試提示渲染
    test_description = "SW Vit C 1000mg Tab 60"
    prompt_info = engine.create_product_matching_prompt(test_description, test_fuzzy_matches)
    
    print(f"\n生成的提示:")
    print("=" * 50)
    print(prompt_info['prompt'])
    
    # 測試回應解析
    test_response = """MATCH: Swisse Vitamin C 1000mg Tablets 60 Pack
REASONING: The description matches the dosage (1000mg), form (tablets), and pack size (60). SW is commonly used as abbreviation for Swisse."""
    
    parsed = engine.parse_ai_response(test_response, 'product_match')
    print(f"\n解析結果:")
    print(f"  匹配: {parsed['match']}")
    print(f"  推理: {parsed['reasoning']}")
    print(f"  解析成功: {parsed['parse_success']}")
    
    # 測試模板驗證
    validation = engine.validate_prompt_template(engine.default_template)
    print(f"\n模板驗證:")
    print(f"  有效: {validation['is_valid']}")
    print(f"  錯誤: {validation['errors']}")
    print(f"  警告: {validation['warnings']}")
