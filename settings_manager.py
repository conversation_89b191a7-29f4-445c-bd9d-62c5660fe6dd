"""
設定管理模組 - 保存和載入用戶設定
Settings Manager Module - Save and load user settings
"""

import json
import os
import logging
from typing import Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class SettingsManager:
    """用戶設定管理器"""
    
    def __init__(self, settings_file: str = "user_settings.json"):
        """
        初始化設定管理器
        
        Args:
            settings_file: 設定文件路徑
        """
        self.settings_file = settings_file
        self.default_settings = {
            # UI Settings
            "language": "en",
            "remember_settings": True,
            "log_panel_width": 400,
            
            # API Keys
            "api_keys": {
                "openai_api_key": "",
                "anthropic_api_key": "",
                "google_api_key": ""
            },
            
            # AI Model Selection
            "selected_models": {
                "gpt-3.5-turbo-0125": False,
                "gpt-4o-mini": False,
                "gpt-4-turbo": False,
                "claude-3-5-sonnet": False,
                "claude-3-opus": False,
                "gemini-2.0-flash-exp": False,
                "gemini-1.5-pro": False
            },
            
            # Processing Settings
            "processing_settings": {
                "voting_threshold": 2,
                "fuzzy_top_n": 5,
                "fuzzy_cutoff": 60,
                "batch_size": 10,
                "filter_duplicates": True,
                "infer_vendors": True,
                "max_candidates": 5,
                "process_all_rows": True,
                "row_limit": 100,
                "start_row": 1,
                "end_row": None
            },
            
            # Column Mapping
            "column_mapping": {
                "store": {
                    "item_description": "Item Description",
                    "department": "Department", 
                    "modified": "Modified"
                },
                "shopify": {
                    "title": "Title",
                    "vendor": "Vendor",
                    "product_category": "Product Category"
                }
            },
            
            # Log Settings
            "log_settings": {
                "log_level": "INFO",
                "show_timestamps": True,
                "max_log_lines": 1000
            }
        }
        
        # 載入現有設定
        self.settings = self.load_settings()
    
    def load_settings(self) -> Dict[str, Any]:
        """載入設定文件"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                
                # 合併預設設定和載入的設定
                settings = self.default_settings.copy()
                self._deep_update(settings, loaded_settings)
                
                logger.info(f"設定已從 {self.settings_file} 載入")
                return settings
            else:
                logger.info("設定文件不存在，使用預設設定")
                return self.default_settings.copy()
                
        except Exception as e:
            logger.error(f"載入設定失敗: {e}")
            return self.default_settings.copy()
    
    def save_settings(self) -> bool:
        """保存設定到文件"""
        try:
            # 確保目錄存在
            os.makedirs(os.path.dirname(self.settings_file) if os.path.dirname(self.settings_file) else ".", exist_ok=True)
            
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=2, ensure_ascii=False)
            
            logger.info(f"設定已保存到 {self.settings_file}")
            return True
            
        except Exception as e:
            logger.error(f"保存設定失敗: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """獲取設定值"""
        keys = key.split('.')
        value = self.settings
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """設定值"""
        keys = key.split('.')
        target = self.settings
        
        # 導航到目標位置
        for k in keys[:-1]:
            if k not in target:
                target[k] = {}
            target = target[k]
        
        # 設定值
        target[keys[-1]] = value
        
        # 如果啟用了記住設定，自動保存
        if self.get('remember_settings', True):
            self.save_settings()
    
    def update_api_keys(self, api_keys: Dict[str, str]) -> None:
        """更新 API 密鑰"""
        current_keys = self.get('api_keys', {})
        current_keys.update(api_keys)
        self.set('api_keys', current_keys)
    
    def update_model_selection(self, model_selection: Dict[str, bool]) -> None:
        """更新 AI 模型選擇"""
        self.set('selected_models', model_selection)
    
    def update_processing_settings(self, processing_settings: Dict[str, Any]) -> None:
        """更新處理設定"""
        current_settings = self.get('processing_settings', {})
        current_settings.update(processing_settings)
        self.set('processing_settings', current_settings)
    
    def update_column_mapping(self, column_mapping: Dict[str, Dict[str, str]]) -> None:
        """更新欄位映射"""
        self.set('column_mapping', column_mapping)
    
    def get_api_keys(self) -> Dict[str, str]:
        """獲取 API 密鑰"""
        return self.get('api_keys', {})
    
    def get_selected_models(self) -> Dict[str, bool]:
        """獲取選中的 AI 模型"""
        return self.get('selected_models', {})
    
    def get_processing_settings(self) -> Dict[str, Any]:
        """獲取處理設定"""
        return self.get('processing_settings', {})
    
    def get_column_mapping(self) -> Dict[str, Dict[str, str]]:
        """獲取欄位映射"""
        return self.get('column_mapping', {})
    
    def reset_to_defaults(self) -> None:
        """重置為預設設定"""
        self.settings = self.default_settings.copy()
        if self.get('remember_settings', True):
            self.save_settings()
        logger.info("設定已重置為預設值")
    
    def export_settings(self, export_path: str) -> bool:
        """匯出設定到指定路徑"""
        try:
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=2, ensure_ascii=False)
            logger.info(f"設定已匯出到 {export_path}")
            return True
        except Exception as e:
            logger.error(f"匯出設定失敗: {e}")
            return False
    
    def import_settings(self, import_path: str) -> bool:
        """從指定路徑導入設定"""
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                imported_settings = json.load(f)
            
            # 驗證並合併設定
            settings = self.default_settings.copy()
            self._deep_update(settings, imported_settings)
            self.settings = settings
            
            if self.get('remember_settings', True):
                self.save_settings()
            
            logger.info(f"設定已從 {import_path} 導入")
            return True
        except Exception as e:
            logger.error(f"導入設定失敗: {e}")
            return False
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict) -> None:
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def get_settings_summary(self) -> Dict[str, Any]:
        """獲取設定摘要"""
        selected_models = [model for model, selected in self.get_selected_models().items() if selected]
        
        return {
            "language": self.get('language'),
            "selected_models_count": len(selected_models),
            "selected_models": selected_models,
            "voting_threshold": self.get('processing_settings.voting_threshold'),
            "batch_size": self.get('processing_settings.batch_size'),
            "api_keys_configured": len([k for k, v in self.get_api_keys().items() if v and v.strip()]),
            "remember_settings": self.get('remember_settings')
        }


# 全域設定管理器實例
settings_manager = SettingsManager()


if __name__ == "__main__":
    # 測試設定管理器
    logging.basicConfig(level=logging.INFO)
    
    sm = SettingsManager("test_settings.json")
    
    # 測試設定和獲取
    sm.set('language', 'zh-TW')
    sm.set('api_keys.openai_api_key', 'test_key')
    sm.set('selected_models.gpt-4o-mini', True)
    
    print("語言設定:", sm.get('language'))
    print("OpenAI API Key:", sm.get('api_keys.openai_api_key'))
    print("GPT-4o mini 選中:", sm.get('selected_models.gpt-4o-mini'))
    
    # 測試設定摘要
    summary = sm.get_settings_summary()
    print("設定摘要:", summary)
    
    # 清理測試文件
    import os
    if os.path.exists("test_settings.json"):
        os.remove("test_settings.json")
