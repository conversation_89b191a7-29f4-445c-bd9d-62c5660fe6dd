"""
Gemini 客戶端 - Google Gemini 2.5 Flash 整合
Gemini Client - Google Gemini 2.5 Flash integration
"""

import logging
from typing import Dict, Any
import google.generativeai as genai
from .base_client import BaseAIClient

logger = logging.getLogger(__name__)


class GeminiClient(BaseAIClient):
    """Google Gemini 客戶端"""
    
    def __init__(self, api_key: str, model_name: str = "gemini-2.0-flash-exp", **kwargs):
        """
        初始化 Gemini 客戶端
        
        Args:
            api_key: Google API 密鑰
            model_name: Gemini 模型名稱
            **kwargs: 其他配置參數
        """
        super().__init__(api_key, model_name, **kwargs)
        
        # 設定 Google AI
        genai.configure(api_key=api_key)
        
        # 創建模型實例
        self.model = genai.GenerativeModel(model_name)
        
        # Gemini 特定配置
        self.temperature = self.config.get('temperature', 0.1)
        self.max_output_tokens = self.config.get('max_tokens', 1000)
        self.top_p = self.config.get('top_p', 1.0)
        self.top_k = self.config.get('top_k', 40)
        
        logger.info(f"Gemini 客戶端初始化完成 - 模型: {model_name}")
    
    def _prepare_request(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """
        準備 Gemini API 請求參數
        
        Args:
            prompt: 輸入提示
            **kwargs: 其他參數
            
        Returns:
            API 請求參數字典
        """
        generation_config = genai.types.GenerationConfig(
            temperature=kwargs.get('temperature', self.temperature),
            max_output_tokens=kwargs.get('max_tokens', self.max_output_tokens),
            top_p=kwargs.get('top_p', self.top_p),
            top_k=kwargs.get('top_k', self.top_k),
        )
        
        return {
            'contents': prompt,
            'generation_config': generation_config
        }
    
    def _parse_response(self, response: Any) -> str:
        """
        解析 Gemini API 回應
        
        Args:
            response: Google AI API 回應對象
            
        Returns:
            解析後的文本
        """
        try:
            content = response.text
            
            # 更新 token 統計（Gemini 的 token 統計可能不同）
            if hasattr(response, 'usage_metadata'):
                prompt_tokens = getattr(response.usage_metadata, 'prompt_token_count', 0)
                completion_tokens = getattr(response.usage_metadata, 'candidates_token_count', 0)
                total_tokens = prompt_tokens + completion_tokens
                self.total_tokens += total_tokens
                logger.debug(f"Gemini Token 使用: {total_tokens} (輸入: {prompt_tokens}, 輸出: {completion_tokens})")
            
            return content.strip() if content else ""
            
        except (AttributeError, IndexError, KeyError) as e:
            logger.error(f"解析 Gemini 回應失敗: {e}")
            return ""
    
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """
        生成 Gemini 回應
        
        Args:
            prompt: 輸入提示
            **kwargs: 其他參數
            
        Returns:
            AI 生成的回應文本
        """
        if not self.validate_api_key():
            raise ValueError("無效的 Google API 密鑰")
        
        # 準備請求參數
        request_params = self._prepare_request(prompt, **kwargs)
        
        try:
            # 調用 Google AI API
            response = await self.model.generate_content_async(**request_params)
            
            # 解析回應
            response_text = self._parse_response(response)
            
            if not response_text:
                raise ValueError("Gemini 回應為空")
            
            return response_text
            
        except Exception as e:
            # Gemini 的錯誤處理可能與其他 API 不同
            error_msg = str(e)
            
            if "quota" in error_msg.lower() or "rate" in error_msg.lower():
                logger.error(f"Gemini API 速率限制: {e}")
                raise Exception(f"API 速率限制: {e}")
            elif "auth" in error_msg.lower() or "key" in error_msg.lower():
                logger.error(f"Gemini API 認證失敗: {e}")
                raise Exception(f"API 認證失敗: {e}")
            else:
                logger.error(f"Gemini 請求失敗: {e}")
                raise Exception(f"請求失敗: {e}")
    
    def estimate_tokens(self, text: str) -> int:
        """
        估算文本的 token 數量
        
        Args:
            text: 輸入文本
            
        Returns:
            估算的 token 數量
        """
        # Gemini 的 token 計算可能與其他模型略有不同
        english_chars = len([c for c in text if ord(c) < 128])
        chinese_chars = len(text) - english_chars
        
        estimated_tokens = (english_chars / 4) + (chinese_chars / 1.5)
        return int(estimated_tokens)
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        獲取模型信息
        
        Returns:
            模型信息字典
        """
        return {
            'provider': 'Google',
            'model': self.model_name,
            'type': 'Generative AI',
            'max_context_length': 1000000,  # Gemini 2.0 Flash 的上下文長度
            'supports_streaming': True,
            'supports_function_calling': True
        }
    
    async def test_connection(self) -> Dict[str, Any]:
        """
        測試 API 連接
        
        Returns:
            測試結果
        """
        test_prompt = "Hello, this is a connection test. Please respond with 'Connection successful.'"
        
        try:
            result = await self.generate_with_retry(test_prompt)
            
            return {
                'success': result['success'],
                'model': self.model_name,
                'response_time': result['duration'],
                'error': result.get('error', None)
            }
            
        except Exception as e:
            return {
                'success': False,
                'model': self.model_name,
                'error': str(e)
            }


if __name__ == "__main__":
    # 測試程式碼
    import asyncio
    import os
    from dotenv import load_dotenv
    
    load_dotenv()
    
    async def test_gemini_client():
        api_key = os.getenv('GOOGLE_API_KEY')
        if not api_key:
            print("請設定 GOOGLE_API_KEY 環境變數")
            return
        
        client = GeminiClient(api_key)
        
        # 測試連接
        print("測試 Gemini 連接...")
        test_result = await client.test_connection()
        print(f"連接測試結果: {test_result}")
        
        if test_result['success']:
            # 測試產品匹配
            test_prompt = """You are a pharmacy product data expert.

Given an internal product name and standard names, identify the best match.

Internal name: "SW Vit C 1000mg Tab 60"

Standard names:
1. Swisse Vitamin C 1000mg Tablets 60 Pack
2. Swisse Vitamin C 500mg Tablets 120 Pack

Format response as:
MATCH: [exact product name]
REASONING: [brief explanation]"""
            
            print("\n測試產品匹配...")
            result = await client.generate_with_retry(test_prompt)
            print(f"回應: {result['response']}")
            print(f"耗時: {result['duration']:.2f}s")
            
            # 顯示統計信息
            stats = client.get_statistics()
            print(f"\n統計信息: {stats}")
    
    # 運行測試
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_gemini_client())
