"""
Claude 客戶端 - Anthropic Claude 4.5 整合
Claude <PERSON>lient - Anthropic Claude 4.5 integration
"""

import logging
from typing import Dict, Any
import anthropic
from .base_client import BaseAIClient

logger = logging.getLogger(__name__)


class ClaudeClient(BaseAIClient):
    """Anthropic Claude 客戶端"""
    
    def __init__(self, api_key: str, model_name: str = "claude-3-5-sonnet-20241022", **kwargs):
        """
        初始化 Claude 客戶端
        
        Args:
            api_key: Anthropic API 密鑰
            model_name: <PERSON> 模型名稱
            **kwargs: 其他配置參數
        """
        super().__init__(api_key, model_name, **kwargs)
        
        # 設定 Anthropic 客戶端
        self.client = anthropic.AsyncAnthropic(api_key=api_key)
        
        # Claude 特定配置
        self.temperature = kwargs.get('temperature', 0.1)
        self.max_tokens = kwargs.get('max_tokens', 1000)
        self.top_p = kwargs.get('top_p', 1.0)
        
        logger.info(f"<PERSON> 客戶端初始化完成 - 模型: {model_name}")
    
    def _prepare_request(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """
        準備 <PERSON> API 請求參數
        
        Args:
            prompt: 輸入提示
            **kwargs: 其他參數
            
        Returns:
            API 請求參數字典
        """
        request_params = {
            'model': self.model_name,
            'messages': [
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'temperature': kwargs.get('temperature', self.temperature),
            'max_tokens': kwargs.get('max_tokens', self.max_tokens),
            'top_p': kwargs.get('top_p', self.top_p),
        }
        
        return request_params
    
    def _parse_response(self, response: Any) -> str:
        """
        解析 Claude API 回應
        
        Args:
            response: Anthropic API 回應對象
            
        Returns:
            解析後的文本
        """
        try:
            content = response.content[0].text
            
            # 更新 token 統計
            if hasattr(response, 'usage'):
                input_tokens = getattr(response.usage, 'input_tokens', 0)
                output_tokens = getattr(response.usage, 'output_tokens', 0)
                total_tokens = input_tokens + output_tokens
                self.total_tokens += total_tokens
                logger.debug(f"Claude Token 使用: {total_tokens} (輸入: {input_tokens}, 輸出: {output_tokens})")
            
            return content.strip() if content else ""
            
        except (AttributeError, IndexError, KeyError) as e:
            logger.error(f"解析 Claude 回應失敗: {e}")
            return ""
    
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """
        生成 Claude 回應
        
        Args:
            prompt: 輸入提示
            **kwargs: 其他參數
            
        Returns:
            AI 生成的回應文本
        """
        if not self.validate_api_key():
            raise ValueError("無效的 Anthropic API 密鑰")
        
        # 準備請求參數
        request_params = self._prepare_request(prompt, **kwargs)
        
        try:
            # 調用 Anthropic API
            response = await self.client.messages.create(**request_params)
            
            # 解析回應
            response_text = self._parse_response(response)
            
            if not response_text:
                raise ValueError("Claude 回應為空")
            
            return response_text
            
        except anthropic.RateLimitError as e:
            logger.error(f"Claude API 速率限制: {e}")
            raise Exception(f"API 速率限制: {e}")
        
        except anthropic.AuthenticationError as e:
            logger.error(f"Claude API 認證失敗: {e}")
            raise Exception(f"API 認證失敗: {e}")
        
        except anthropic.APIError as e:
            logger.error(f"Claude API 錯誤: {e}")
            raise Exception(f"API 錯誤: {e}")
        
        except Exception as e:
            logger.error(f"Claude 請求失敗: {e}")
            raise Exception(f"請求失敗: {e}")
    
    def estimate_tokens(self, text: str) -> int:
        """
        估算文本的 token 數量
        
        Args:
            text: 輸入文本
            
        Returns:
            估算的 token 數量
        """
        # Claude 的 token 計算與 GPT 類似
        english_chars = len([c for c in text if ord(c) < 128])
        chinese_chars = len(text) - english_chars
        
        estimated_tokens = (english_chars / 4) + (chinese_chars / 1.5)
        return int(estimated_tokens)
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        獲取模型信息
        
        Returns:
            模型信息字典
        """
        return {
            'provider': 'Anthropic',
            'model': self.model_name,
            'type': 'Messages',
            'max_context_length': 200000,  # Claude 3.5 Sonnet 的上下文長度
            'supports_streaming': True,
            'supports_function_calling': True
        }
    
    async def test_connection(self) -> Dict[str, Any]:
        """
        測試 API 連接
        
        Returns:
            測試結果
        """
        test_prompt = "Hello, this is a connection test. Please respond with 'Connection successful.'"
        
        try:
            result = await self.generate_with_retry(test_prompt)
            
            return {
                'success': result['success'],
                'model': self.model_name,
                'response_time': result['duration'],
                'error': result.get('error', None)
            }
            
        except Exception as e:
            return {
                'success': False,
                'model': self.model_name,
                'error': str(e)
            }


if __name__ == "__main__":
    # 測試程式碼
    import asyncio
    import os
    from dotenv import load_dotenv
    
    load_dotenv()
    
    async def test_claude_client():
        api_key = os.getenv('ANTHROPIC_API_KEY')
        if not api_key:
            print("請設定 ANTHROPIC_API_KEY 環境變數")
            return
        
        client = ClaudeClient(api_key)
        
        # 測試連接
        print("測試 Claude 連接...")
        test_result = await client.test_connection()
        print(f"連接測試結果: {test_result}")
        
        if test_result['success']:
            # 測試產品匹配
            test_prompt = """You are a pharmacy product data expert.

Given an internal product name and standard names, identify the best match.

Internal name: "SW Vit C 1000mg Tab 60"

Standard names:
1. Swisse Vitamin C 1000mg Tablets 60 Pack
2. Swisse Vitamin C 500mg Tablets 120 Pack

Format response as:
MATCH: [exact product name]
REASONING: [brief explanation]"""
            
            print("\n測試產品匹配...")
            result = await client.generate_with_retry(test_prompt)
            print(f"回應: {result['response']}")
            print(f"耗時: {result['duration']:.2f}s")
            
            # 顯示統計信息
            stats = client.get_statistics()
            print(f"\n統計信息: {stats}")
    
    # 運行測試
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_claude_client())
