"""
AI 品牌推斷模組 - 使用 AI 模型從產品描述推斷品牌
AI Vendor Inference Module - Use AI models to infer brand from product description
"""

import logging
import yaml
from typing import Dict, List, Optional, Tuple
import re
import json

logger = logging.getLogger(__name__)


class VendorInferenceEngine:
    """使用 AI 模型推斷產品品牌的引擎"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化品牌推斷引擎
        
        Args:
            config_path: 配置文件路徑
        """
        self.config = self._load_config(config_path)
        self.valid_vendors = set()
        self.ai_clients = {}  # 將在後續模組中初始化
        
        # 品牌推斷提示模板
        self.inference_prompt = """
You are a pharmacy product expert. Given a product description, identify the most likely brand/vendor from the provided list.

Product Description: "{product_description}"

Valid Vendors:
{vendor_list}

Instructions:
- Analyze the product description for brand indicators (brand names, product lines, packaging style)
- Choose ONLY from the provided vendor list
- If no clear brand can be identified, return "UNKNOWN"
- Provide reasoning for your choice

Format your response as:
VENDOR: [exact vendor name from list or UNKNOWN]
REASONING: [brief explanation]
CONFIDENCE: [HIGH/MEDIUM/LOW]
"""
    
    def _load_config(self, config_path: str) -> Dict:
        """載入配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            logger.error(f"配置文件未找到: {config_path}")
            raise
        except yaml.YAMLError as e:
            logger.error(f"配置文件格式錯誤: {e}")
            raise
    
    def set_valid_vendors(self, vendors: List[str]):
        """
        設定有效的供應商清單
        
        Args:
            vendors: 有效供應商名稱清單
        """
        self.valid_vendors = set(vendor.strip() for vendor in vendors if vendor.strip())
        logger.info(f"設定 {len(self.valid_vendors)} 個有效供應商用於 AI 推斷")
    
    def set_ai_clients(self, ai_clients: Dict):
        """
        設定 AI 客戶端
        
        Args:
            ai_clients: AI 客戶端字典
        """
        self.ai_clients = ai_clients
        logger.info(f"設定 {len(ai_clients)} 個 AI 客戶端")
    
    def _extract_brand_keywords(self, product_description: str) -> List[str]:
        """
        從產品描述中提取可能的品牌關鍵字
        
        Args:
            product_description: 產品描述
            
        Returns:
            品牌關鍵字清單
        """
        if not product_description:
            return []
        
        # 常見的品牌指示詞模式
        brand_patterns = [
            r'\b([A-Z][a-z]+\'?s?)\s+(?:brand|by|from)\b',  # "Swisse brand", "by Blackmores"
            r'\b([A-Z][A-Z]+)\b',  # 全大寫縮寫 "SW", "BL"
            r'\b([A-Z][a-z]+)\s+(?:vitamin|supplement|tablet|capsule)\b',  # "Swisse vitamin"
            r'^([A-Z][a-z]+)\s',  # 開頭的大寫單詞
        ]
        
        keywords = []
        for pattern in brand_patterns:
            matches = re.findall(pattern, product_description, re.IGNORECASE)
            keywords.extend(matches)
        
        # 移除重複並過濾常見非品牌詞
        common_words = {'vitamin', 'supplement', 'tablet', 'capsule', 'mg', 'iu', 'pack', 'bottle'}
        keywords = list(set(word for word in keywords if word.lower() not in common_words))
        
        return keywords
    
    def _rule_based_inference(self, product_description: str) -> Optional[str]:
        """
        基於規則的品牌推斷（作為 AI 推斷的補充）
        
        Args:
            product_description: 產品描述
            
        Returns:
            推斷的品牌名稱或 None
        """
        if not product_description:
            return None
        
        description_lower = product_description.lower()
        
        # 直接匹配有效供應商名稱
        for vendor in self.valid_vendors:
            if vendor.lower() in description_lower:
                logger.debug(f"規則推斷找到品牌: {vendor}")
                return vendor
        
        # 提取關鍵字並嘗試匹配
        keywords = self._extract_brand_keywords(product_description)
        for keyword in keywords:
            for vendor in self.valid_vendors:
                if keyword.lower() in vendor.lower() or vendor.lower().startswith(keyword.lower()):
                    logger.debug(f"關鍵字匹配找到品牌: {keyword} -> {vendor}")
                    return vendor
        
        return None
    
    def _parse_ai_response(self, response_text: str) -> Dict[str, str]:
        """
        解析 AI 回應
        
        Args:
            response_text: AI 回應文本
            
        Returns:
            解析結果字典
        """
        result = {
            'vendor': 'UNKNOWN',
            'reasoning': 'Failed to parse response',
            'confidence': 'LOW'
        }
        
        if not response_text:
            return result
        
        # 解析格式化回應
        lines = response_text.strip().split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('VENDOR:'):
                vendor = line.replace('VENDOR:', '').strip()
                if vendor in self.valid_vendors or vendor == 'UNKNOWN':
                    result['vendor'] = vendor
            elif line.startswith('REASONING:'):
                result['reasoning'] = line.replace('REASONING:', '').strip()
            elif line.startswith('CONFIDENCE:'):
                confidence = line.replace('CONFIDENCE:', '').strip().upper()
                if confidence in ['HIGH', 'MEDIUM', 'LOW']:
                    result['confidence'] = confidence
        
        return result
    
    async def infer_vendor_with_ai(self, product_description: str, 
                                  model_name: str = 'gpt4o') -> Dict[str, any]:
        """
        使用指定的 AI 模型推斷品牌
        
        Args:
            product_description: 產品描述
            model_name: AI 模型名稱
            
        Returns:
            推斷結果字典
        """
        if not self.valid_vendors:
            raise ValueError("尚未設定有效供應商清單")
        
        if model_name not in self.ai_clients:
            raise ValueError(f"AI 客戶端 '{model_name}' 未設定")
        
        # 準備提示
        vendor_list = '\n'.join(f"- {vendor}" for vendor in sorted(self.valid_vendors))
        prompt = self.inference_prompt.format(
            product_description=product_description,
            vendor_list=vendor_list
        )
        
        try:
            # 調用 AI 客戶端
            ai_client = self.ai_clients[model_name]
            response = await ai_client.generate_response(prompt)
            
            # 解析回應
            parsed_result = self._parse_ai_response(response)
            
            result = {
                'model': model_name,
                'vendor': parsed_result['vendor'],
                'reasoning': parsed_result['reasoning'],
                'confidence': parsed_result['confidence'],
                'raw_response': response,
                'success': True
            }
            
            logger.debug(f"{model_name} 推斷結果: {parsed_result['vendor']} ({parsed_result['confidence']})")
            return result
            
        except Exception as e:
            logger.error(f"{model_name} 推斷失敗: {e}")
            return {
                'model': model_name,
                'vendor': 'UNKNOWN',
                'reasoning': f'AI 推斷失敗: {str(e)}',
                'confidence': 'LOW',
                'raw_response': '',
                'success': False,
                'error': str(e)
            }
    
    async def infer_vendor_multi_model(self, product_description: str) -> Dict[str, any]:
        """
        使用多個 AI 模型推斷品牌並投票
        
        Args:
            product_description: 產品描述
            
        Returns:
            綜合推斷結果
        """
        # 首先嘗試規則推斷
        rule_based_result = self._rule_based_inference(product_description)
        
        # 獲取啟用的 AI 模型
        enabled_models = [
            model for model, enabled in self.config.get('ai_models', {}).items()
            if enabled and model in self.ai_clients
        ]
        
        if not enabled_models:
            logger.warning("沒有啟用的 AI 模型")
            return {
                'final_vendor': rule_based_result or 'UNKNOWN',
                'confidence': 'LOW' if rule_based_result else 'VERY_LOW',
                'method': 'rule_based_only',
                'ai_results': [],
                'rule_based_result': rule_based_result
            }
        
        # 並行調用所有 AI 模型
        ai_results = []
        for model in enabled_models:
            result = await self.infer_vendor_with_ai(product_description, model)
            ai_results.append(result)
        
        # 投票決定最終結果
        final_result = self._vote_on_vendor(ai_results, rule_based_result)
        final_result['ai_results'] = ai_results
        final_result['rule_based_result'] = rule_based_result
        
        return final_result
    
    def _vote_on_vendor(self, ai_results: List[Dict], rule_based_result: Optional[str]) -> Dict[str, any]:
        """
        對 AI 推斷結果進行投票
        
        Args:
            ai_results: AI 推斷結果清單
            rule_based_result: 規則推斷結果
            
        Returns:
            投票結果
        """
        # 統計投票
        votes = {}
        confidence_scores = {'HIGH': 3, 'MEDIUM': 2, 'LOW': 1}
        
        # AI 模型投票
        for result in ai_results:
            if result['success'] and result['vendor'] != 'UNKNOWN':
                vendor = result['vendor']
                confidence_weight = confidence_scores.get(result['confidence'], 1)
                votes[vendor] = votes.get(vendor, 0) + confidence_weight
        
        # 規則推斷加權（如果有結果）
        if rule_based_result and rule_based_result in self.valid_vendors:
            votes[rule_based_result] = votes.get(rule_based_result, 0) + 2  # 規則推斷權重為 2
        
        # 決定最終結果
        if not votes:
            return {
                'final_vendor': 'UNKNOWN',
                'confidence': 'LOW',
                'method': 'no_consensus',
                'vote_details': {}
            }
        
        # 找到得票最高的品牌
        winner = max(votes.items(), key=lambda x: x[1])
        winning_vendor, winning_score = winner
        
        # 計算信心度
        total_possible_score = len(ai_results) * 3 + (2 if rule_based_result else 0)
        confidence_ratio = winning_score / total_possible_score if total_possible_score > 0 else 0
        
        if confidence_ratio >= 0.7:
            confidence = 'HIGH'
        elif confidence_ratio >= 0.4:
            confidence = 'MEDIUM'
        else:
            confidence = 'LOW'
        
        return {
            'final_vendor': winning_vendor,
            'confidence': confidence,
            'method': 'ai_voting',
            'vote_details': votes,
            'winning_score': winning_score,
            'total_possible_score': total_possible_score
        }


if __name__ == "__main__":
    # 測試程式碼
    logging.basicConfig(level=logging.INFO)
    
    # 創建測試的推斷引擎
    engine = VendorInferenceEngine()
    
    # 設定測試的有效供應商
    test_vendors = [
        "Swisse", "Ostelin", "Blackmores", "Nature's Own", 
        "Vitamins", "Fish Oil Co", "Healthy Life"
    ]
    engine.set_valid_vendors(test_vendors)
    
    # 測試產品描述
    test_descriptions = [
        "Swisse Vitamin C 1000mg Tablets 60 Pack",
        "BL Fish Oil 1000mg Capsules",
        "Ostelin Vitamin D 1000IU Drops",
        "SW Multivitamin for Women",
        "Generic Vitamin B Complex",
        "Nature's Own Calcium Magnesium",
        "Unknown Brand Protein Powder"
    ]
    
    print("品牌推斷測試結果:")
    print("=" * 80)
    
    for description in test_descriptions:
        # 測試規則推斷
        rule_result = engine._rule_based_inference(description)
        
        # 提取關鍵字
        keywords = engine._extract_brand_keywords(description)
        
        print(f"\n產品描述: '{description}'")
        print(f"  提取關鍵字: {keywords}")
        print(f"  規則推斷結果: {rule_result}")
        
        # 注意：實際的 AI 推斷需要設定 AI 客戶端，這裡只是展示結構
