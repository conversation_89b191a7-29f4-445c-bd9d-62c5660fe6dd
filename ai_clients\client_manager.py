"""
AI 客戶端管理器 - 統一管理所有 AI 客戶端
AI Client Manager - Unified management of all AI clients
"""

import logging
import yaml
import os
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv

from .gpt_client import GPTClient
from .claude_client import ClaudeClient
from .gemini_client import GeminiClient

logger = logging.getLogger(__name__)


class AIClientManager:
    """AI 客戶端管理器，負責初始化和管理所有 AI 客戶端"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化 AI 客戶端管理器
        
        Args:
            config_path: 配置文件路徑
        """
        # 載入環境變數
        load_dotenv()
        
        self.config = self._load_config(config_path)
        self.clients = {}
        self.enabled_models = {}
        
        # 初始化客戶端
        self._initialize_clients()
        
        logger.info(f"AI 客戶端管理器初始化完成，啟用 {len(self.clients)} 個客戶端")
    
    def _load_config(self, config_path: str) -> Dict:
        """載入配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            logger.error(f"配置文件未找到: {config_path}")
            raise
        except yaml.YAMLError as e:
            logger.error(f"配置文件格式錯誤: {e}")
            raise
    
    def _initialize_clients(self):
        """初始化所有啟用的 AI 客戶端"""
        ai_models_config = self.config.get('ai_models', {})
        
        # GPT 客戶端
        if ai_models_config.get('gpt4o', False):
            api_key = os.getenv('OPENAI_API_KEY')
            if api_key and api_key != 'your_openai_api_key_here':
                try:
                    self.clients['gpt4o'] = GPTClient(api_key, model_name="gpt-4o-mini")
                    self.enabled_models['gpt4o'] = True
                    logger.info("GPT-4o mini 客戶端初始化成功")
                except Exception as e:
                    logger.error(f"GPT 客戶端初始化失敗: {e}")
                    self.enabled_models['gpt4o'] = False
            else:
                logger.warning("OPENAI_API_KEY 未設定或為預設值，跳過 GPT 客戶端")
                self.enabled_models['gpt4o'] = False
        
        # Claude 客戶端
        if ai_models_config.get('claude', False):
            api_key = os.getenv('ANTHROPIC_API_KEY')
            if api_key and api_key != 'your_anthropic_api_key_here':
                try:
                    self.clients['claude'] = ClaudeClient(api_key, model_name="claude-3-5-sonnet-20241022")
                    self.enabled_models['claude'] = True
                    logger.info("Claude 3.5 Sonnet 客戶端初始化成功")
                except Exception as e:
                    logger.error(f"Claude 客戶端初始化失敗: {e}")
                    self.enabled_models['claude'] = False
            else:
                logger.warning("ANTHROPIC_API_KEY 未設定或為預設值，跳過 Claude 客戶端")
                self.enabled_models['claude'] = False
        
        # Gemini 客戶端
        if ai_models_config.get('gemini', False):
            api_key = os.getenv('GOOGLE_API_KEY')
            if api_key and api_key != 'your_google_api_key_here':
                try:
                    self.clients['gemini'] = GeminiClient(api_key, model_name="gemini-2.0-flash-exp")
                    self.enabled_models['gemini'] = True
                    logger.info("Gemini 2.0 Flash 客戶端初始化成功")
                except Exception as e:
                    logger.error(f"Gemini 客戶端初始化失敗: {e}")
                    self.enabled_models['gemini'] = False
            else:
                logger.warning("GOOGLE_API_KEY 未設定或為預設值，跳過 Gemini 客戶端")
                self.enabled_models['gemini'] = False
    
    def get_client(self, model_name: str):
        """
        獲取指定的 AI 客戶端
        
        Args:
            model_name: 模型名稱
            
        Returns:
            AI 客戶端實例
        """
        if model_name not in self.clients:
            raise ValueError(f"客戶端 '{model_name}' 未初始化或不可用")
        
        return self.clients[model_name]
    
    def get_available_clients(self) -> List[str]:
        """
        獲取所有可用的客戶端名稱
        
        Returns:
            可用客戶端名稱清單
        """
        return list(self.clients.keys())
    
    def get_enabled_models(self) -> Dict[str, bool]:
        """
        獲取所有模型的啟用狀態
        
        Returns:
            模型啟用狀態字典
        """
        return self.enabled_models.copy()
    
    async def test_all_connections(self) -> Dict[str, Dict]:
        """
        測試所有客戶端的連接
        
        Returns:
            所有客戶端的測試結果
        """
        results = {}
        
        for model_name, client in self.clients.items():
            logger.info(f"測試 {model_name} 連接...")
            try:
                result = await client.test_connection()
                results[model_name] = result
            except Exception as e:
                results[model_name] = {
                    'success': False,
                    'model': model_name,
                    'error': str(e)
                }
        
        return results
    
    async def generate_responses_parallel(self, prompt: str, 
                                        models: Optional[List[str]] = None) -> Dict[str, Dict]:
        """
        並行生成多個模型的回應
        
        Args:
            prompt: 輸入提示
            models: 指定的模型清單（可選，默認使用所有可用模型）
            
        Returns:
            所有模型的回應結果
        """
        import asyncio
        
        if models is None:
            models = list(self.clients.keys())
        
        # 過濾可用的模型
        available_models = [m for m in models if m in self.clients]
        
        if not available_models:
            logger.warning("沒有可用的模型")
            return {}
        
        # 創建並行任務
        tasks = {}
        for model_name in available_models:
            client = self.clients[model_name]
            tasks[model_name] = client.generate_with_retry(prompt)
        
        # 等待所有任務完成
        results = {}
        completed_tasks = await asyncio.gather(*tasks.values(), return_exceptions=True)
        
        for model_name, result in zip(available_models, completed_tasks):
            if isinstance(result, Exception):
                results[model_name] = {
                    'success': False,
                    'model': model_name,
                    'error': str(result),
                    'response': ''
                }
            else:
                results[model_name] = result
        
        return results
    
    def get_all_statistics(self) -> Dict[str, Dict]:
        """
        獲取所有客戶端的統計信息
        
        Returns:
            所有客戶端的統計信息
        """
        stats = {}
        for model_name, client in self.clients.items():
            stats[model_name] = client.get_statistics()
        
        return stats
    
    def reset_all_statistics(self):
        """重置所有客戶端的統計信息"""
        for client in self.clients.values():
            client.reset_statistics()
        logger.info("重置所有客戶端統計信息")
    
    def get_model_info_summary(self) -> Dict[str, Dict]:
        """
        獲取所有模型的信息摘要
        
        Returns:
            模型信息摘要
        """
        info = {}
        for model_name, client in self.clients.items():
            info[model_name] = client.get_model_info()
        
        return info


if __name__ == "__main__":
    # 測試程式碼
    import asyncio
    
    async def test_client_manager():
        logging.basicConfig(level=logging.INFO)
        
        # 創建客戶端管理器
        manager = AIClientManager()
        
        print(f"可用客戶端: {manager.get_available_clients()}")
        print(f"模型啟用狀態: {manager.get_enabled_models()}")
        
        # 測試所有連接
        print("\n測試所有客戶端連接...")
        connection_results = await manager.test_all_connections()
        for model, result in connection_results.items():
            status = "成功" if result['success'] else f"失敗: {result.get('error', 'Unknown')}"
            print(f"  {model}: {status}")
        
        # 如果有可用的客戶端，測試並行生成
        available_clients = manager.get_available_clients()
        if available_clients:
            print(f"\n測試並行生成（使用 {len(available_clients)} 個客戶端）...")
            test_prompt = "What is 2+2? Please answer briefly."
            
            parallel_results = await manager.generate_responses_parallel(test_prompt)
            for model, result in parallel_results.items():
                if result['success']:
                    print(f"  {model}: {result['response'][:50]}...")
                else:
                    print(f"  {model}: 失敗 - {result.get('error', 'Unknown')}")
            
            # 顯示統計信息
            print(f"\n統計信息:")
            stats = manager.get_all_statistics()
            for model, stat in stats.items():
                print(f"  {model}: {stat['request_count']} 請求, {stat['success_rate']:.1f}% 成功率")
        else:
            print("\n沒有可用的客戶端進行測試")
    
    # 運行測試
    asyncio.run(test_client_manager())
