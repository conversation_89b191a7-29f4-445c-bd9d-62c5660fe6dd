"""
測試修復後的 AI 客戶端
Test Fixed AI Clients
"""

import os
import logging
from dotenv import load_dotenv
from ai_clients.client_manager import AIClientManager

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def test_ai_client_manager():
    """測試 AI 客戶端管理器"""
    print("🤖 測試 AI 客戶端管理器 / Testing AI Client Manager")
    print("=" * 60)
    
    try:
        # 載入環境變數
        load_dotenv()
        
        # 檢查環境變數
        print("\n🔑 檢查環境變數:")
        env_keys = {
            "OPENAI_API_KEY": os.getenv("OPENAI_API_KEY"),
            "ANTHROPIC_API_KEY": os.getenv("ANTHROPIC_API_KEY"),
            "GOOGLE_API_KEY": os.getenv("GOOGLE_API_KEY")
        }
        
        for key, value in env_keys.items():
            if value and value not in ['your_openai_api_key_here', 'your_anthropic_api_key_here', 'your_google_api_key_here']:
                masked_value = value[:8] + "..." + value[-8:] if len(value) > 16 else value
                print(f"  ✅ {key}: {masked_value}")
            else:
                print(f"  ❌ {key}: 未設定或為預設值")
        
        # 初始化 AI 客戶端管理器
        print("\n🔧 初始化 AI 客戶端管理器...")
        ai_manager = AIClientManager()
        
        print(f"\n📊 初始化結果:")
        print(f"  總客戶端數: {len(ai_manager.clients)}")
        print(f"  啟用的客戶端: {list(ai_manager.clients.keys())}")
        print(f"  啟用狀態: {ai_manager.enabled_models}")
        
        # 測試每個客戶端
        print(f"\n🧪 測試客戶端功能:")
        for client_name, client in ai_manager.clients.items():
            try:
                print(f"  ✅ {client_name}: 客戶端已初始化")
                # 這裡可以添加簡單的 API 調用測試
                # response = client.test_connection()
            except Exception as e:
                print(f"  ❌ {client_name}: 測試失敗 - {e}")
        
        return ai_manager
        
    except Exception as e:
        print(f"❌ AI 客戶端管理器初始化失敗: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_vendor_inference_with_ai():
    """測試帶 AI 的供應商推斷"""
    print("\n🔍 測試帶 AI 的供應商推斷 / Testing Vendor Inference with AI")
    print("=" * 60)
    
    try:
        from vendor_infer import VendorInferenceEngine
        
        # 初始化 AI 客戶端管理器
        ai_manager = test_ai_client_manager()
        
        if not ai_manager or not ai_manager.clients:
            print("❌ 沒有可用的 AI 客戶端，跳過測試")
            return
        
        # 初始化供應商推斷引擎
        vendor_inferrer = VendorInferenceEngine()
        
        # 設定測試供應商
        test_vendors = ["Blackmores", "Swisse", "Nature's Way", "Ostelin", "Centrum"]
        vendor_inferrer.set_valid_vendors(test_vendors)
        
        # 設定 AI 客戶端
        vendor_inferrer.set_ai_clients(ai_manager.clients)
        
        # 測試產品描述
        test_products = [
            "Blackmores Vitamin D3 1000IU Capsules",
            "Swisse Ultiboost Vitamin C 1000mg",
            "Nature's Way Kids Smart Omega-3",
            "Ostelin Vitamin D & K2 Capsules",
            "Unknown Brand Calcium Tablets"
        ]
        
        print(f"\n🧪 測試產品推斷:")
        for i, description in enumerate(test_products, 1):
            print(f"\n  測試 {i}: {description}")
            
            try:
                # 執行推斷
                result = vendor_inferrer.infer_vendor(description)
                
                print(f"    推斷結果: {result.get('vendor', 'Unknown')}")
                print(f"    信心度: {result.get('confidence', 'Unknown')}")
                print(f"    方法: {result.get('method', 'Unknown')}")
                
                if result.get('ai_responses'):
                    print(f"    AI 回應數: {len(result['ai_responses'])}")
                
            except Exception as e:
                print(f"    ❌ 推斷失敗: {e}")
        
    except Exception as e:
        print(f"❌ 供應商推斷測試失敗: {e}")
        import traceback
        traceback.print_exc()

def test_simple_processing():
    """測試簡單的處理流程"""
    print("\n⚙️ 測試簡單的處理流程 / Testing Simple Processing Flow")
    print("=" * 60)
    
    try:
        from main import ProductMappingSystem
        
        # 初始化系統
        print("🔧 初始化產品映射系統...")
        system = ProductMappingSystem()
        
        # 檢查 AI 客戶端狀態
        print(f"\n📊 AI 客戶端狀態:")
        print(f"  總客戶端數: {len(system.ai_manager.clients)}")
        print(f"  啟用的客戶端: {list(system.ai_manager.clients.keys())}")
        
        if system.ai_manager.clients:
            print("✅ AI 客戶端已正確初始化！")
        else:
            print("❌ 沒有啟用的 AI 客戶端")
        
    except Exception as e:
        print(f"❌ 處理流程測試失敗: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主測試函數"""
    print("🧪 修復後的 AI 客戶端測試")
    print("🧪 Fixed AI Clients Test")
    print("=" * 80)
    
    try:
        # 測試 AI 客戶端管理器
        ai_manager = test_ai_client_manager()
        
        # 測試供應商推斷
        test_vendor_inference_with_ai()
        
        # 測試簡單處理流程
        test_simple_processing()
        
        print("\n" + "=" * 80)
        print("📊 測試總結 / Test Summary")
        print("=" * 80)
        
        if ai_manager and ai_manager.clients:
            print(f"\n🎉 AI 功能修復成功！")
            print(f"✅ 啟用的 AI 模型: {list(ai_manager.clients.keys())}")
            print(f"✅ 客戶端數量: {len(ai_manager.clients)}")
            
            print(f"\n🚀 現在可以:")
            print(f"  1. 在 Streamlit 界面中重新執行處理")
            print(f"  2. AI 投票機制將正常運作")
            print(f"  3. 供應商推斷將使用 AI 模型")
            print(f"  4. 結果表格將顯示真實的 AI 投票數")
            
        else:
            print(f"\n⚠️ AI 功能仍有問題")
            print(f"❌ 請檢查:")
            print(f"  1. API 金鑰是否有效")
            print(f"  2. 網路連接是否正常")
            print(f"  3. config.yaml 配置是否正確")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
