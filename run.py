"""
快速運行腳本 - AI 產品名稱映射系統
Quick Run Script - AI Product Name Mapping System
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


def check_requirements():
    """檢查必要的依賴是否已安裝"""
    try:
        import pandas
        import rapidfuzz
        import streamlit
        import openai
        import anthropic
        import google.generativeai
        print("✅ 所有必要依賴已安裝")
        return True
    except ImportError as e:
        print(f"❌ 缺少依賴: {e}")
        print("請運行: pip install -r requirements.txt")
        return False


def check_env_file():
    """檢查環境變數文件"""
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️  .env 文件不存在")
        print("請複製 .env.example 到 .env 並填入您的 API 密鑰")
        return False
    
    print("✅ .env 文件存在")
    return True


def create_sample_data():
    """創建示例資料文件"""
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    
    # 創建示例藥房清單
    store_sample = """Item Description,Department,Modified
SW Vitamin C 1000mg Tablets 60 Pack,Vitamins,2024-01-01 10:00:00
BL Fish Oil 1000mg Capsules 200,Supplements,2024-01-02 11:00:00
Ostelin Vitamin D 1000IU Drops 20ml,Vitamins,2024-01-03 12:00:00
NAT Calcium Magnesium 200 Tablets,Minerals,2024-01-04 13:00:00
SW Multivitamin Women 60 Tablets,Vitamins,2024-01-05 14:00:00"""
    
    store_file = data_dir / "store_list_sample.csv"
    with open(store_file, 'w', encoding='utf-8') as f:
        f.write(store_sample)
    
    # 創建示例 Shopify 清單
    shopify_sample = """Title,Vendor,Product Category
Swisse Vitamin C 1000mg Tablets 60 Pack,Swisse,Vitamins
Swisse Vitamin C 500mg Tablets 120 Pack,Swisse,Vitamins
Swisse Multivitamin for Women 60 Tablets,Swisse,Vitamins
Blackmores Fish Oil 1000mg Capsules 200,Blackmores,Supplements
Blackmores Fish Oil 1000mg Capsules 400,Blackmores,Supplements
Ostelin Vitamin D 1000IU Drops 20ml,Ostelin,Vitamins
Ostelin Vitamin D 2000IU Tablets 90,Ostelin,Vitamins
Nature's Own Calcium Magnesium 200 Tablets,Nature's Own,Minerals
Nature's Own Iron Complex 100 Tablets,Nature's Own,Minerals"""
    
    shopify_file = data_dir / "shopify_list_sample.csv"
    with open(shopify_file, 'w', encoding='utf-8') as f:
        f.write(shopify_sample)
    
    print(f"✅ 示例資料已創建:")
    print(f"   藥房清單: {store_file}")
    print(f"   Shopify 清單: {shopify_file}")
    
    return store_file, shopify_file


def run_streamlit():
    """運行 Streamlit 應用"""
    print("🚀 啟動 Streamlit 應用...")
    subprocess.run([sys.executable, "-m", "streamlit", "run", "streamlit_app.py"])


def run_cli(store_file, shopify_file, batch_size=5):
    """運行命令行版本"""
    print("🚀 運行命令行版本...")
    cmd = [
        sys.executable, "main.py",
        "--store-file", str(store_file),
        "--shopify-file", str(shopify_file),
        "--batch-size", str(batch_size)
    ]
    subprocess.run(cmd)


def run_tests():
    """運行測試"""
    print("🧪 運行測試...")
    subprocess.run([sys.executable, "-m", "pytest", "tests/", "-v"])


def main():
    """主函數"""
    parser = argparse.ArgumentParser(description='AI 產品名稱映射系統 - 快速運行腳本')
    parser.add_argument('mode', choices=['gui', 'cli', 'test', 'setup'], 
                       help='運行模式: gui(Streamlit界面), cli(命令行), test(測試), setup(設置)')
    parser.add_argument('--store-file', help='藥房清單文件路徑 (cli 模式)')
    parser.add_argument('--shopify-file', help='Shopify 清單文件路徑 (cli 模式)')
    parser.add_argument('--batch-size', type=int, default=5, help='批次大小 (cli 模式)')
    parser.add_argument('--create-sample', action='store_true', help='創建示例資料')
    
    args = parser.parse_args()
    
    print("🧠 AI 產品名稱映射系統")
    print("=" * 50)
    
    if args.mode == 'setup':
        print("🔧 設置系統...")
        
        # 檢查依賴
        if not check_requirements():
            return
        
        # 檢查環境文件
        check_env_file()
        
        # 創建示例資料
        if args.create_sample:
            create_sample_data()
        
        print("\n✅ 設置完成！")
        print("下一步:")
        print("1. 確保 .env 文件包含有效的 API 密鑰")
        print("2. 運行 'python run.py gui' 啟動圖形界面")
        print("3. 或運行 'python run.py cli --store-file data/store_list_sample.csv --shopify-file data/shopify_list_sample.csv'")
        
    elif args.mode == 'gui':
        if not check_requirements():
            return
        run_streamlit()
        
    elif args.mode == 'cli':
        if not check_requirements():
            return
        
        if args.store_file and args.shopify_file:
            run_cli(args.store_file, args.shopify_file, args.batch_size)
        else:
            # 使用示例資料
            if not Path("data/store_list_sample.csv").exists():
                print("創建示例資料...")
                store_file, shopify_file = create_sample_data()
            else:
                store_file = "data/store_list_sample.csv"
                shopify_file = "data/shopify_list_sample.csv"
            
            run_cli(store_file, shopify_file, args.batch_size)
    
    elif args.mode == 'test':
        if not check_requirements():
            return
        run_tests()


if __name__ == "__main__":
    main()
