"""
主要功能測試 - AI 產品名稱映射系統
Main Functionality Tests - AI Product Name Mapping System
"""

import pytest
import pandas as pd
import asyncio
import tempfile
import os
from pathlib import Path
import yaml

# 導入要測試的模組
from loader import DataLoader
from filter_latest import LatestDataFilter
from brand_map import BrandMapper
from fuzzy_match import FuzzyMatcher
from prompt_engine import PromptEngine
from voter import AIVoter
from exporter import ResultExporter
from logger import AILogger


class TestDataLoader:
    """測試資料載入器"""
    
    def setup_method(self):
        """設定測試環境"""
        self.loader = DataLoader()
        
        # 創建測試資料
        self.test_store_data = pd.DataFrame({
            'Item Description': [
                'SW Vitamin C 1000mg Tablets 60 Pack',
                'BL Fish Oil 1000mg Capsules 200',
                'Ostelin Vitamin D 1000IU Drops 20ml'
            ],
            'Department': ['Vitamins', 'Supplements', 'Vitamins'],
            'Modified': ['2024-01-01', '2024-01-02', '2024-01-03']
        })
        
        self.test_shopify_data = pd.DataFrame({
            'Title': [
                'Swisse Vitamin C 1000mg Tablets 60 Pack',
                'Blackmores Fish Oil 1000mg Capsules 200',
                'Ostelin Vitamin D 1000IU Drops 20ml'
            ],
            'Vendor': ['Swisse', 'Blackmores', 'Ostelin'],
            'Product Category': ['Vitamins', 'Supplements', 'Vitamins']
        })
    
    def test_load_store_list(self):
        """測試載入藥房清單"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            self.test_store_data.to_csv(f.name, index=False)
            
            try:
                df = self.loader.load_store_list(f.name)
                assert len(df) == 3
                assert 'item_description' in df.columns
                assert 'department' in df.columns
                assert 'modified' in df.columns
            finally:
                os.unlink(f.name)
    
    def test_load_shopify_list(self):
        """測試載入 Shopify 清單"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            self.test_shopify_data.to_csv(f.name, index=False)
            
            try:
                df = self.loader.load_shopify_list(f.name)
                assert len(df) == 3
                assert 'title' in df.columns
                assert 'vendor' in df.columns
            finally:
                os.unlink(f.name)
    
    def test_get_unique_vendors(self):
        """測試獲取唯一供應商"""
        vendors = self.loader.get_unique_vendors(self.test_shopify_data)
        expected_vendors = ['Blackmores', 'Ostelin', 'Swisse']
        assert vendors == expected_vendors


class TestLatestDataFilter:
    """測試重複資料過濾器"""
    
    def setup_method(self):
        """設定測試環境"""
        self.filter_engine = LatestDataFilter()
        
        # 創建包含重複項目的測試資料
        self.test_data = pd.DataFrame({
            'item_description': [
                'Vitamin C 1000mg',
                'Vitamin C 1000mg',  # 重複
                'Fish Oil 1000mg',
                'Vitamin C 1000mg',  # 重複
                'Vitamin D 2000IU'
            ],
            'department': ['Vitamins', 'Vitamins', 'Supplements', 'Vitamins', 'Vitamins'],
            'modified': [
                '2024-01-01 10:00:00',
                '2024-01-02 11:00:00',
                '2024-01-01 12:00:00',
                '2024-01-03 09:00:00',  # 最新的 Vitamin C
                '2024-01-01 14:00:00'
            ]
        })
    
    def test_filter_latest_by_description(self):
        """測試按描述過濾最新項目"""
        filtered_df = self.filter_engine.filter_latest_by_description(self.test_data)
        
        # 應該只保留 3 個唯一項目
        assert len(filtered_df) == 3
        
        # 檢查 Vitamin C 是否保留了最新的版本
        vitamin_c_rows = filtered_df[filtered_df['item_description'] == 'Vitamin C 1000mg']
        assert len(vitamin_c_rows) == 1
        assert vitamin_c_rows.iloc[0]['modified'] == '2024-01-03 09:00:00'
    
    def test_get_duplicate_statistics(self):
        """測試獲取重複統計"""
        stats = self.filter_engine.get_duplicate_statistics(self.test_data)
        
        assert stats['total_rows'] == 5
        assert stats['unique_descriptions'] == 3
        assert stats['duplicates'] == 2
        assert stats['duplicate_rate'] == 0.4


class TestBrandMapper:
    """測試品牌映射器"""
    
    def setup_method(self):
        """設定測試環境"""
        # 創建測試配置
        test_config = {
            'brand_abbreviations': {
                'SW': 'Swisse',
                'BL': 'Blackmores',
                'OST': 'Ostelin'
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(test_config, f)
            self.config_file = f.name
        
        self.brand_mapper = BrandMapper(self.config_file)
        self.brand_mapper.set_valid_vendors(['Swisse', 'Blackmores', 'Ostelin', 'Nature\'s Own'])
    
    def teardown_method(self):
        """清理測試環境"""
        os.unlink(self.config_file)
    
    def test_expand_abbreviation(self):
        """測試縮寫擴展"""
        assert self.brand_mapper.expand_abbreviation('SW') == 'Swisse'
        assert self.brand_mapper.expand_abbreviation('BL') == 'Blackmores'
        assert self.brand_mapper.expand_abbreviation('UNKNOWN') is None
    
    def test_validate_vendor(self):
        """測試供應商驗證"""
        assert self.brand_mapper.validate_vendor('Swisse') is True
        assert self.brand_mapper.validate_vendor('Unknown Brand') is False
    
    def test_process_brand(self):
        """測試品牌處理流程"""
        # 測試縮寫擴展
        result = self.brand_mapper.process_brand('SW')
        assert result['expanded'] == 'Swisse'
        assert result['is_valid'] is True
        assert result['final_vendor'] == 'Swisse'
        
        # 測試直接有效品牌
        result = self.brand_mapper.process_brand('Blackmores')
        assert result['is_valid'] is True
        assert result['final_vendor'] == 'Blackmores'
        
        # 測試無效品牌
        result = self.brand_mapper.process_brand('Unknown')
        assert result['is_valid'] is False
        assert len(result['suggestions']) >= 0


class TestFuzzyMatcher:
    """測試模糊匹配器"""
    
    def setup_method(self):
        """設定測試環境"""
        self.fuzzy_matcher = FuzzyMatcher()
        
        self.test_shopify_data = pd.DataFrame({
            'title': [
                'Swisse Vitamin C 1000mg Tablets 60 Pack',
                'Swisse Vitamin C 500mg Tablets 120 Pack',
                'Blackmores Fish Oil 1000mg Capsules 200',
                'Ostelin Vitamin D 1000IU Drops 20ml'
            ],
            'vendor': ['Swisse', 'Swisse', 'Blackmores', 'Ostelin'],
            'product_category': ['Vitamins', 'Vitamins', 'Supplements', 'Vitamins']
        })
    
    def test_find_best_matches(self):
        """測試尋找最佳匹配"""
        query = 'SW Vit C 1000mg Tab 60'
        candidates = self.test_shopify_data['title'].tolist()
        
        matches = self.fuzzy_matcher.find_best_matches(
            query, candidates, shopify_df=self.test_shopify_data
        )
        
        assert len(matches) > 0
        # 第一個匹配應該是最相似的
        assert 'Vitamin C 1000mg' in matches[0]['matched_title']
        assert matches[0]['primary_score'] > 50
    
    def test_match_product_by_vendor(self):
        """測試按供應商匹配產品"""
        query = 'Vit C 1000mg'
        vendor = 'Swisse'
        
        matches = self.fuzzy_matcher.match_product_by_vendor(
            query, vendor, self.test_shopify_data
        )
        
        assert len(matches) > 0
        # 所有匹配都應該是 Swisse 品牌
        for match in matches:
            assert match['shopify_info']['vendor'] == 'Swisse'


class TestPromptEngine:
    """測試提示引擎"""
    
    def setup_method(self):
        """設定測試環境"""
        self.prompt_engine = PromptEngine()
    
    def test_prepare_candidates(self):
        """測試準備候選項目"""
        fuzzy_matches = [
            {'matched_title': 'Product A', 'primary_score': 95},
            {'matched_title': 'Product B', 'primary_score': 85},
            {'matched_title': 'Product C', 'primary_score': 75}
        ]
        
        candidates = self.prompt_engine.prepare_candidates(fuzzy_matches)
        
        assert len(candidates) == 5  # 應該填充到 5 個
        assert candidates[0] == 'Product A'
        assert candidates[1] == 'Product B'
        assert candidates[2] == 'Product C'
        assert candidates[3] == ''  # 空填充
        assert candidates[4] == ''  # 空填充
    
    def test_render_prompt(self):
        """測試提示渲染"""
        item_description = 'SW Vit C 1000mg'
        candidates = ['Product A', 'Product B', '', '', '']
        
        prompt = self.prompt_engine.render_prompt(item_description, candidates)
        
        assert 'SW Vit C 1000mg' in prompt
        assert 'Product A' in prompt
        assert 'Product B' in prompt
    
    def test_parse_ai_response(self):
        """測試 AI 回應解析"""
        response_text = """MATCH: Swisse Vitamin C 1000mg Tablets 60 Pack
REASONING: The description matches the dosage and pack size perfectly."""
        
        parsed = self.prompt_engine.parse_ai_response(response_text, 'product_match')
        
        assert parsed['match'] == 'Swisse Vitamin C 1000mg Tablets 60 Pack'
        assert 'dosage and pack size' in parsed['reasoning']
        assert parsed['parse_success'] is True


class TestAIVoter:
    """測試 AI 投票器"""
    
    def setup_method(self):
        """設定測試環境"""
        self.voter = AIVoter()
    
    def test_vote_on_product_match(self):
        """測試產品匹配投票"""
        ai_results = [
            {
                'model': 'gpt4o',
                'success': True,
                'response': {
                    'match': 'Product A',
                    'reasoning': 'Perfect match',
                    'confidence': 'HIGH'
                }
            },
            {
                'model': 'claude',
                'success': True,
                'response': {
                    'match': 'Product A',
                    'reasoning': 'Clear match',
                    'confidence': 'HIGH'
                }
            }
        ]
        
        candidates = ['Product A', 'Product B', 'Product C']
        
        result = self.voter.vote_on_product_match(ai_results, candidates)
        
        assert result['final_match'] == 'Product A'
        assert result['confidence'] in ['HIGH', 'MEDIUM']
        assert result['needs_review'] is False
        assert result['vote_count'] == 2


class TestResultExporter:
    """測試結果匯出器"""
    
    def setup_method(self):
        """設定測試環境"""
        self.exporter = ResultExporter()
    
    def test_create_summary_report(self):
        """測試創建摘要報告"""
        test_results = [
            {
                'voting_result': {
                    'needs_review': False,
                    'confidence': 'HIGH',
                    'agreement_level': 'STRONG_CONSENSUS'
                }
            },
            {
                'voting_result': {
                    'needs_review': True,
                    'confidence': 'LOW',
                    'agreement_level': 'NO_CONSENSUS'
                }
            }
        ]
        
        summary = self.exporter.create_summary_report(test_results)
        
        assert summary['total_processed'] == 2
        assert summary['successful_matches'] == 1
        assert summary['needs_review'] == 1
        assert summary['success_rate'] == 0.5
        assert summary['review_rate'] == 0.5


@pytest.mark.asyncio
async def test_integration_workflow():
    """整合測試 - 測試完整工作流程"""
    # 創建測試資料
    store_data = pd.DataFrame({
        'Item Description': ['SW Vit C 1000mg Tab 60'],
        'Department': ['Vitamins'],
        'Modified': ['2024-01-01']
    })
    
    shopify_data = pd.DataFrame({
        'Title': ['Swisse Vitamin C 1000mg Tablets 60 Pack'],
        'Vendor': ['Swisse'],
        'Product Category': ['Vitamins']
    })
    
    # 初始化組件
    filter_engine = LatestDataFilter()
    brand_mapper = BrandMapper()
    fuzzy_matcher = FuzzyMatcher()
    
    # 設定有效供應商
    valid_vendors = ['Swisse']
    brand_mapper.set_valid_vendors(valid_vendors)
    
    # 執行工作流程
    # 1. 過濾重複項目
    filtered_df = filter_engine.filter_latest_by_description(store_data)
    assert len(filtered_df) == 1
    
    # 2. 品牌處理
    brand_result = brand_mapper.process_brand('SW')
    assert brand_result['is_valid'] is True
    assert brand_result['final_vendor'] == 'Swisse'
    
    # 3. 模糊匹配
    matches = fuzzy_matcher.match_product_by_vendor(
        'SW Vit C 1000mg Tab 60', 'Swisse', shopify_data
    )
    assert len(matches) > 0
    assert 'Vitamin C 1000mg' in matches[0]['matched_title']
    
    print("✅ 整合測試通過！")


if __name__ == "__main__":
    # 運行測試
    pytest.main([__file__, "-v"])
