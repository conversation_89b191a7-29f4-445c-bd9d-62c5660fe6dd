#!/usr/bin/env python3
"""
測試修復的腳本
Test script for the fixes
"""

import pandas as pd
import asyncio
from streamlit_app import ProductMappingApp

def test_column_fixes():
    """測試欄位名稱修復"""
    print("🧪 測試欄位名稱修復...")
    
    try:
        # 載入示例資料
        store_df = pd.read_csv('Data/store_list_sample.csv')
        shopify_df = pd.read_csv('Data/shopify_list_sample.csv')
        
        print(f"Store columns: {list(store_df.columns)}")
        print(f"Shopify columns: {list(shopify_df.columns)}")
        
        # 測試 ProductMappingApp 初始化
        app = ProductMappingApp()
        
        # 測試 get_unique_vendors
        vendors = app.data_loader.get_unique_vendors(shopify_df)
        print(f"✅ 找到供應商: {vendors}")
        
        # 測試模糊匹配
        test_query = "SW Vitamin C 1000mg Tab 60"
        matches = app.fuzzy_matcher.find_best_matches(
            test_query, 
            shopify_df['Title'].tolist(), 
            shopify_df=shopify_df
        )
        
        if matches:
            print(f"✅ 模糊匹配成功，找到 {len(matches)} 個匹配")
            print(f"   最佳匹配: {matches[0]['matched_title']}")
            print(f"   分數: {matches[0]['primary_score']:.1f}")
        else:
            print("⚠️ 沒有找到匹配項目")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_processing():
    """測試處理流程"""
    print("\n🧪 測試處理流程...")
    
    try:
        app = ProductMappingApp()
        
        # 載入示例資料
        store_df = pd.read_csv('Data/store_list_sample.csv')
        shopify_df = pd.read_csv('Data/shopify_list_sample.csv')
        
        # 模擬處理單個項目
        item_data = store_df.iloc[0].to_dict()
        valid_vendors = app.data_loader.get_unique_vendors(shopify_df)
        
        result = app._process_single_item_sync(item_data, shopify_df, valid_vendors)
        
        print(f"✅ 處理成功")
        print(f"   原始產品: {result['store_data'].get('Item Description', 'N/A')}")
        print(f"   最終匹配: {result['voting_result']['final_match']}")
        print(f"   信心度: {result['voting_result']['confidence']}")
        print(f"   需要審查: {result['voting_result']['needs_review']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 處理測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主測試函數"""
    print("🚀 開始測試修復...")
    
    # 測試欄位名稱修復
    test1_passed = test_column_fixes()
    
    # 測試處理流程
    test2_passed = test_processing()
    
    # 總結
    print(f"\n📊 測試結果:")
    print(f"   欄位名稱修復: {'✅ 通過' if test1_passed else '❌ 失敗'}")
    print(f"   處理流程測試: {'✅ 通過' if test2_passed else '❌ 失敗'}")
    
    if test1_passed and test2_passed:
        print(f"\n🎉 所有測試通過！系統已準備就緒。")
        print(f"💡 現在可以運行 Streamlit 應用:")
        print(f"   python -m streamlit run streamlit_app.py")
    else:
        print(f"\n⚠️ 部分測試失敗，請檢查錯誤信息。")

if __name__ == "__main__":
    main()
