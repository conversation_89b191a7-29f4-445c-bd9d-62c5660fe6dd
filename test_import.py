"""
文件導入測試腳本 - 測試各種文件格式的導入功能
File Import Test Script - Test importing various file formats
"""

import pandas as pd
import os
import glob
from pathlib import Path


def scan_data_files():
    """掃描 data 目錄中的所有文件"""
    print("🔍 掃描 data 目錄中的文件...")
    
    data_dir = Path("data")
    if not data_dir.exists():
        print("❌ data 目錄不存在")
        return
    
    # 查找所有支援的文件
    patterns = ["*.csv", "*.xlsx", "*.xls"]
    files = []
    
    for pattern in patterns:
        files.extend(data_dir.rglob(pattern))
    
    if not files:
        print("📁 沒有找到任何支援的文件")
        return
    
    print(f"✅ 找到 {len(files)} 個文件:")
    for i, file in enumerate(files, 1):
        size = file.stat().st_size / 1024 / 1024  # MB
        print(f"  {i}. {file} ({size:.1f} MB)")
    
    return files


def test_file_loading(file_path):
    """測試載入指定文件"""
    print(f"\n📂 測試載入文件: {file_path}")
    
    try:
        file_extension = file_path.suffix.lower()
        
        if file_extension == '.csv':
            df = pd.read_csv(file_path)
        elif file_extension in ['.xlsx', '.xls']:
            df = pd.read_excel(file_path)
        else:
            print(f"❌ 不支援的文件格式: {file_extension}")
            return None
        
        print(f"✅ 載入成功！")
        print(f"   行數: {len(df)}")
        print(f"   欄位數: {len(df.columns)}")
        print(f"   記憶體使用: {df.memory_usage(deep=True).sum() / 1024 / 1024:.1f} MB")
        
        print(f"\n📋 欄位清單:")
        for i, col in enumerate(df.columns, 1):
            non_null = df[col].count()
            null_count = df[col].isnull().sum()
            unique_count = df[col].nunique()
            print(f"   {i:2d}. {col}")
            print(f"       類型: {df[col].dtype}")
            print(f"       非空: {non_null}, 空值: {null_count}, 唯一值: {unique_count}")
        
        print(f"\n🔍 前5行預覽:")
        print(df.head().to_string())
        
        return df
        
    except Exception as e:
        print(f"❌ 載入失敗: {e}")
        return None


def analyze_store_data(df):
    """分析藥房資料結構"""
    print(f"\n🏪 分析藥房資料結構...")
    
    # 檢查必要欄位
    required_columns = ['Item Description', 'Department', 'Modified']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        print(f"❌ 缺少必要欄位: {missing_columns}")
        
        # 嘗試找到相似的欄位
        print(f"🔍 可能的替代欄位:")
        for missing_col in missing_columns:
            similar_cols = [col for col in df.columns if missing_col.lower() in col.lower() or col.lower() in missing_col.lower()]
            if similar_cols:
                print(f"   {missing_col} -> {similar_cols}")
    else:
        print(f"✅ 所有必要欄位都存在")
    
    # 分析資料品質
    if 'Item Description' in df.columns:
        desc_col = df['Item Description']
        empty_descriptions = desc_col.isnull().sum()
        duplicate_descriptions = desc_col.duplicated().sum()
        
        print(f"\n📊 產品描述分析:")
        print(f"   空值數量: {empty_descriptions}")
        print(f"   重複數量: {duplicate_descriptions}")
        print(f"   唯一產品: {desc_col.nunique()}")
        
        if duplicate_descriptions > 0:
            print(f"   重複率: {duplicate_descriptions / len(df) * 100:.1f}%")
    
    if 'Department' in df.columns:
        dept_col = df['Department']
        unique_depts = dept_col.nunique()
        empty_depts = dept_col.isnull().sum()
        
        print(f"\n🏷️ 部門/品牌分析:")
        print(f"   唯一部門數: {unique_depts}")
        print(f"   空值數量: {empty_depts}")
        
        if unique_depts <= 20:
            print(f"   部門清單: {dept_col.value_counts().head(10).to_dict()}")


def analyze_shopify_data(df):
    """分析 Shopify 資料結構"""
    print(f"\n🛒 分析 Shopify 資料結構...")
    
    # 檢查必要欄位
    required_columns = ['Title', 'Vendor']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        print(f"❌ 缺少必要欄位: {missing_columns}")
        
        # 嘗試找到相似的欄位
        print(f"🔍 可能的替代欄位:")
        for missing_col in missing_columns:
            similar_cols = [col for col in df.columns if missing_col.lower() in col.lower() or col.lower() in missing_col.lower()]
            if similar_cols:
                print(f"   {missing_col} -> {similar_cols}")
    else:
        print(f"✅ 所有必要欄位都存在")
    
    # 分析供應商
    if 'Vendor' in df.columns:
        vendor_col = df['Vendor']
        unique_vendors = vendor_col.nunique()
        
        print(f"\n🏭 供應商分析:")
        print(f"   唯一供應商數: {unique_vendors}")
        print(f"   前10個供應商:")
        
        vendor_counts = vendor_col.value_counts().head(10)
        for vendor, count in vendor_counts.items():
            print(f"     {vendor}: {count} 個產品")
    
    # 分析產品標題
    if 'Title' in df.columns:
        title_col = df['Title']
        empty_titles = title_col.isnull().sum()
        duplicate_titles = title_col.duplicated().sum()
        
        print(f"\n📝 產品標題分析:")
        print(f"   空值數量: {empty_titles}")
        print(f"   重複數量: {duplicate_titles}")
        print(f"   唯一產品: {title_col.nunique()}")


def main():
    """主函數"""
    print("🧠 AI 產品名稱映射系統 - 文件導入測試")
    print("=" * 60)
    
    # 掃描文件
    files = scan_data_files()
    if not files:
        return
    
    print(f"\n請選擇要測試的文件 (1-{len(files)}):")
    try:
        choice = int(input("輸入文件編號: ")) - 1
        if 0 <= choice < len(files):
            selected_file = files[choice]
            
            # 載入文件
            df = test_file_loading(selected_file)
            if df is None:
                return
            
            # 根據文件名判斷類型並分析
            file_name = selected_file.name.lower()
            if 'store' in file_name or 'shop' in str(selected_file).lower():
                analyze_store_data(df)
            elif 'shopify' in file_name:
                analyze_shopify_data(df)
            else:
                print(f"\n❓ 無法自動判斷文件類型，請手動選擇:")
                print("1. 藥房清單 (Store List)")
                print("2. Shopify 清單")
                
                type_choice = input("選擇類型 (1 或 2): ")
                if type_choice == "1":
                    analyze_store_data(df)
                elif type_choice == "2":
                    analyze_shopify_data(df)
        else:
            print("❌ 無效的選擇")
            
    except ValueError:
        print("❌ 請輸入有效的數字")
    except KeyboardInterrupt:
        print("\n👋 測試中斷")


if __name__ == "__main__":
    main()
