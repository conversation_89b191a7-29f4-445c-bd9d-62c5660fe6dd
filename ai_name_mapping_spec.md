# 🧠 AI-Based Product Name Mapping System (Store List → Shopify List)

## 📌 Project Objective

This system standardizes inconsistent pharmacy product names ("store list") by matching them to the correct entries in the master product list ("Shopify list"). The matching process combines fuzzy logic with AI model voting (GPT-4o mini, Claude 4.5, Gemini 2.5 Flash), and is designed for scalable, automated integration inside Augment or Cursor AI coding platforms.

---

## 📁 File Definitions

### ✅ Store List (pharmacy input)

- Input source that may contain:
  - Incomplete, incorrect, or abbreviated product names
  - Missing or invalid brand (vendor) information
- **Relevant Columns Only**:
  - `Item Description` → Dirty product name (main matching input)
  - `Department` → Brand name (may be missing; system must infer or correct it)
  - `Modified` → Timestamp for filtering latest version of duplicates

### ✅ Shopify List (canonical master reference)

- Clean, verified product list (single source of truth)
- **Required Columns Only**:
  - `Title` → Canonical product name
  - `Vendor` → Valid brand list
  - `Product Category` → Optional, useful for reporting or SEO tagging
- ❌ Exclude `Body (HTML)` to improve performance and simplify structure

---

## 🔄 Workflow Summary

### Step 1: Preprocessing

- For each `Item Description` in the store list:
  - Keep only the row with the most recent `Modified` timestamp
  - Discard older duplicates

### Step 2: Vendor Matching (Brand Inference)

- If `Department` is empty or not found in Shopify's `Vendor` list:
  - Apply brand abbreviation map (e.g. `SW → Swisse`, `OST → Ostelin`)
  - If still unresolved, use AI (GPT/Claude/Gemini) to infer correct brand from `Item Description`
- Only allow vendors found in the Shopify list
- If brand cannot be resolved → mark as `needs_review`

### Step 3: Product Matching (Scoped by Brand)

- Once vendor is established:
  - Filter Shopify list to matching `Vendor`
  - Use `rapidfuzz` to extract top 5 closest `Title` matches
  - Feed `Item Description` and Top-5 `Title` matches to each AI model
  - Prompt AI: "Which of these standard names best matches the internal product name? Why?"

### Step 4: AI Voting Mechanism

- Each AI model returns:
  - Best match title (must be from the given Top 5)
  - Reasoning
- Final match is accepted if:
  - At least 2 of 3 models agree → `confidence = High`
  - All models differ → `needs_review = True`

### Step 5: Output Construction

- Combine:
  - Original store list row
  - Matched Shopify row (Title, Vendor, Product Category)
  - AI decisions (votes, reasoning)
  - Confidence & review status

```csv
| Item Description | Department | Modified | Matched Vendor | Final Title | Category | GPT-4o | Claude | Gemini | Final Match | Confidence | Needs Review |
```

---

## 🧱 Module Architecture (Cursor-ready)

| File               | Function                                                                   |
| ------------------ | -------------------------------------------------------------------------- |
| `loader.py`        | Load CSVs and apply column filters                                         |
| `filter_latest.py` | Remove outdated duplicates by `Modified` timestamp                         |
| `brand_map.py`     | Expand brand abbreviations into valid vendor names                         |
| `vendor_infer.py`  | AI-based vendor inference from description if needed                       |
| `fuzzy_match.py`   | Run `rapidfuzz` match between dirty name and brand-specific Shopify titles |
| `prompt_engine.py` | Render prompt and pass Top-N candidates to each AI                         |
| `ai_clients/`      | GPT, Claude, Gemini integration wrappers                                   |
| `voter.py`         | Aggregate AI outputs and select final match or flag for review             |
| `exporter.py`      | Output results CSV, review list, and logs                                  |
| `logger.py`        | Save structured AI outputs per row for traceability                        |

---

## 🔧 Prompt Template (prompts/default\_prompt.txt)

```txt
You are a pharmacy product data expert.

Given an internal (possibly incorrect) product name, and a list of standard product names under the same vendor (brand), identify which standard name is the best match.

Internal name:
"{item_description}"

Standard name list:
{standard_name_1}
{standard_name_2}
{standard_name_3}
{standard_name_4}
{standard_name_5}

Instructions:
- Choose only one best match from the list.
- Base your decision on ingredients, volume, dosage form, and naming style.
- Return:
1. The best matching product name.
2. Short reasoning (1-2 sentences).
```

---

## 🛠️ Configuration

Stored in `config.yaml`:

```yaml
ai_models:
  gpt4o: true
  claude: true
  gemini: true
voting:
  threshold: 2   # 2/3 agreement = match
vendor_from: shopify_list_only  # enforce Shopify-only brand matching
prompt_path: prompts/default_prompt.txt
```

---

## 📤 Outputs

1. `matched_results.csv` – Final results with AI decision columns
2. `needs_review.csv` – Any rows with unresolved vendor or product
3. `logs/ai_votes.jsonl` – Raw prompt + response log for every row (debuggable)

---

## 🧠 Key Rules Recap

- Only use `Item Description`, `Department`, `Modified` from the store list
- Only use `Title`, `Vendor`, and optionally `Product Category` from the Shopify list
- `Vendor` in store list must come from Shopify list – if not present, infer or reject
- Always use latest row per product (based on `Modified`)
- Match decisions must be explainable (AI reasoning output required)

---

## 🖥️ Streamlit GUI Layout (optional)

```plaintext
[🔼 Upload: store_list.csv]      [🔼 Upload: shopify_list.csv]
[🧠 AI Model Selection] ☑ GPT-4o ☑ Claude ☑ Gemini

[🛠️ Start Mapping Button]

[📊 Results Table View]
| Item Description | Final Title | Confidence | AI Votes | Needs Review |

[💾 Export to CSV]     [⚠️ Show Review List Only]
```

---

## ✅ Cursor/Augment Deployment Ready

This markdown spec is ready to be used inside Cursor or Augment for:

- Modular AI code development
- Streamlit GUI interface
- Testing and batch automation

Let me know if you'd like a Streamlit layout, function scaffold, prompt generator, or dataset splitter next.

