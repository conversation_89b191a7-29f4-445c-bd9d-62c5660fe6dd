{"language": "en", "remember_settings": true, "log_panel_width": 400, "api_keys": {"openai_api_key": "********************************************************************************************************************************************************************", "anthropic_api_key": "************************************************************************************************************", "google_api_key": "AIzaSyB7ngDDgErKfqclwSCJLoktfssCKcftuxQ"}, "selected_models": {"gpt-3.5-turbo-0125": false, "gpt-4o-mini": true, "gpt-4-turbo": false, "claude-3-5-sonnet": true, "claude-3-opus": false, "gemini-2.0-flash-exp": true, "gemini-1.5-pro": false}, "processing_settings": {"voting_threshold": 2, "fuzzy_top_n": 5, "fuzzy_cutoff": 60, "batch_size": 15, "filter_duplicates": false, "infer_vendors": true, "max_candidates": 5, "process_all_rows": true, "row_limit": null}, "column_mapping": {"store": {"item_description": "Item Description", "department": "Department", "modified": "Modified"}, "shopify": {"title": "Title", "vendor": "<PERSON><PERSON><PERSON>", "product_category": "Product Category"}}, "log_settings": {"log_level": "INFO", "show_timestamps": true, "max_log_lines": 1000}}