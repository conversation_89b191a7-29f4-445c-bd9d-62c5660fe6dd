# 🧠 AI-Based Product Name Mapping System

一個基於 AI 的產品名稱映射系統，用於將藥房產品清單（Store List）標準化映射到 Shopify 主清單。系統結合模糊邏輯與 AI 模型投票（GPT-4o mini、Claude 3.5、Gemini 2.0 Flash），專為可擴展的自動化整合而設計。

## 📌 專案目標

將不一致的藥房產品名稱（"store list"）匹配到主產品清單（"Shopify list"）中的正確條目。匹配過程結合模糊邏輯與 AI 模型投票，設計用於在 Augment 或 Cursor AI 編碼平台內進行可擴展的自動化整合。

## 🚀 快速開始

### 1. 環境設置

```bash
# 克隆專案
git clone <repository-url>
cd ai-product-mapping

# 安裝依賴
pip install -r requirements.txt

# 設置環境變數
cp .env.example .env
# 編輯 .env 文件，填入您的 API 密鑰
```

### 2. 配置 API 密鑰

在 `.env` 文件中設置您的 API 密鑰：

```env
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
```

### 3. 運行系統

#### 方式一：圖形界面（推薦）

```bash
python run.py gui
```

#### 方式二：命令行

```bash
# 使用示例資料
python run.py cli

# 使用自定義文件
python run.py cli --store-file your_store_list.csv --shopify-file your_shopify_list.csv
```

#### 方式三：直接運行

```bash
python main.py --store-file data/store_list.csv --shopify-file data/shopify_list.csv
```

## 📁 文件結構

```
ai-product-mapping/
├── 📄 main.py                 # 主程式入口
├── 📄 streamlit_app.py        # Streamlit GUI 應用
├── 📄 run.py                  # 快速運行腳本
├── 📄 config.yaml             # 系統配置文件
├── 📄 requirements.txt        # Python 依賴
├── 📄 .env.example           # 環境變數範例
├── 📁 ai_clients/            # AI 客戶端模組
│   ├── 📄 __init__.py
│   ├── 📄 base_client.py     # 基礎 AI 客戶端
│   ├── 📄 gpt_client.py      # GPT-4o mini 客戶端
│   ├── 📄 claude_client.py   # Claude 3.5 客戶端
│   ├── 📄 gemini_client.py   # Gemini 2.0 客戶端
│   └── 📄 client_manager.py  # AI 客戶端管理器
├── 📁 prompts/               # AI 提示模板
│   └── 📄 default_prompt.txt
├── 📁 data/                  # 資料文件目錄
├── 📁 output/                # 輸出結果目錄
├── 📁 logs/                  # 日誌文件目錄
├── 📁 tests/                 # 測試文件
│   └── 📄 test_main.py
├── 📄 loader.py              # 資料載入模組
├── 📄 filter_latest.py       # 重複資料過濾
├── 📄 brand_map.py           # 品牌映射模組
├── 📄 vendor_infer.py        # AI 品牌推斷
├── 📄 fuzzy_match.py         # 模糊匹配模組
├── 📄 prompt_engine.py       # 提示引擎
├── 📄 voter.py               # AI 投票機制
├── 📄 exporter.py            # 結果匯出器
└── 📄 logger.py              # 日誌記錄模組
```

## 🔄 工作流程

### 步驟 1: 資料預處理
- 載入 Store List 和 Shopify List CSV 文件
- 驗證必要欄位：`Item Description`、`Department`、`Modified`（Store List）
- 驗證必要欄位：`Title`、`Vendor`、`Product Category`（Shopify List）

### 步驟 2: 重複項目過濾
- 根據 `Modified` 時間戳保留每個產品描述的最新版本
- 移除過期的重複項目

### 步驟 3: 品牌映射和推斷
- 使用品牌縮寫映射（例如：`SW → Swisse`、`BL → Blackmores`）
- 如果品牌無法解析，使用 AI 模型從產品描述推斷正確品牌
- 只允許 Shopify 清單中存在的供應商

### 步驟 4: 模糊匹配
- 使用 `rapidfuzz` 在指定品牌範圍內提取前 5 個最接近的 `Title` 匹配
- 基於成分、容量、劑型和命名風格進行匹配

### 步驟 5: AI 投票機制
- 將產品描述和前 5 個候選項目發送給每個 AI 模型
- 每個 AI 模型返回最佳匹配和推理
- 最終匹配通過投票決定：
  - 至少 2/3 模型同意 → `confidence = High`
  - 所有模型不同 → `needs_review = True`

### 步驟 6: 結果輸出
- 生成包含原始資料、匹配結果、AI 決策和信心度的綜合 CSV
- 創建需要人工審查的項目清單
- 保存詳細的 AI 投票日誌以便追蹤

## 📊 輸出文件

系統會生成以下輸出文件：

1. **`matched_results_YYYYMMDD_HHMMSS.csv`** - 完整匹配結果
2. **`needs_review_YYYYMMDD_HHMMSS.csv`** - 需要人工審查的項目
3. **`statistics_YYYYMMDD_HHMMSS.json`** - 處理統計報告
4. **`logs/ai_votes.jsonl`** - 詳細的 AI 投票日誌

## ⚙️ 配置選項

在 `config.yaml` 中可以配置：

- **AI 模型啟用/禁用**：選擇使用哪些 AI 模型
- **投票閾值**：決定共識所需的最小投票數
- **模糊匹配參數**：相似度閾值和候選項目數量
- **品牌縮寫映射**：自定義品牌縮寫擴展規則
- **文件路徑**：輸入輸出目錄配置

## 🧪 測試

運行測試套件：

```bash
# 運行所有測試
python run.py test

# 或直接使用 pytest
pytest tests/ -v
```

## 📈 性能監控

系統提供詳細的性能監控：

- **處理統計**：成功率、審查率、處理時間
- **AI 模型性能**：各模型的準確性和響應時間
- **匹配質量**：信心度分佈和協議水平
- **詳細日誌**：每個決策的完整追蹤記錄

## 🔧 故障排除

### 常見問題

1. **API 密鑰錯誤**
   ```
   確保 .env 文件中的 API 密鑰正確且有效
   ```

2. **依賴安裝失敗**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

3. **CSV 文件格式錯誤**
   ```
   確保 CSV 文件包含必要的欄位並使用 UTF-8 編碼
   ```

4. **記憶體不足**
   ```
   減少批次大小：--batch-size 5
   ```

### 日誌查看

查看詳細日誌：
```bash
tail -f logs/system.log
```

## 🤝 貢獻

歡迎貢獻！請遵循以下步驟：

1. Fork 專案
2. 創建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 開啟 Pull Request

## 📝 許可證

本專案採用 MIT 許可證 - 詳見 [LICENSE](LICENSE) 文件。

## 🙏 致謝

- OpenAI GPT-4o mini
- Anthropic Claude 3.5 Sonnet
- Google Gemini 2.0 Flash
- RapidFuzz 模糊匹配庫
- Streamlit 網頁應用框架

---

**注意**：本系統需要有效的 AI API 密鑰才能正常運行。請確保您有足夠的 API 配額來處理您的資料量。
