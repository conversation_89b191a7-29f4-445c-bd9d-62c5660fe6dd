"""
資料載入模組 - 載入和驗證 CSV 文件
Data Loader Module - Load and validate CSV files
"""

import pandas as pd
import yaml
import logging
from typing import Dict, List, Optional, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)


class DataLoader:
    """CSV 資料載入器，處理欄位過濾和資料驗證"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化資料載入器
        
        Args:
            config_path: 配置文件路徑
        """
        self.config = self._load_config(config_path)
        self.store_columns = self.config['columns']['store_list']
        self.shopify_columns = self.config['columns']['shopify_list']
    
    def _load_config(self, config_path: str) -> Dict:
        """載入配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            logger.error(f"配置文件未找到: {config_path}")
            raise
        except yaml.YAMLError as e:
            logger.error(f"配置文件格式錯誤: {e}")
            raise
    
    def load_store_list(self, file_path: str) -> pd.DataFrame:
        """
        載入藥房產品清單 (Store List)
        
        Args:
            file_path: CSV 文件路徑
            
        Returns:
            過濾後的 DataFrame
        """
        logger.info(f"載入藥房產品清單: {file_path}")
        
        try:
            # 載入 CSV
            df = pd.read_csv(file_path, encoding='utf-8')
            logger.info(f"原始資料行數: {len(df)}")
            
            # 檢查必要欄位
            required_columns = list(self.store_columns.values())
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                raise ValueError(f"缺少必要欄位: {missing_columns}")
            
            # 只保留需要的欄位
            df_filtered = df[required_columns].copy()
            
            # 重新命名欄位為標準名稱
            column_mapping = {v: k for k, v in self.store_columns.items()}
            df_filtered = df_filtered.rename(columns=column_mapping)
            
            # 資料清理
            df_filtered = self._clean_store_data(df_filtered)
            
            logger.info(f"過濾後資料行數: {len(df_filtered)}")
            return df_filtered
            
        except Exception as e:
            logger.error(f"載入藥房產品清單失敗: {e}")
            raise
    
    def load_shopify_list(self, file_path: str) -> pd.DataFrame:
        """
        載入 Shopify 主清單
        
        Args:
            file_path: CSV 文件路徑
            
        Returns:
            過濾後的 DataFrame
        """
        logger.info(f"載入 Shopify 主清單: {file_path}")
        
        try:
            # 載入 CSV
            df = pd.read_csv(file_path, encoding='utf-8')
            logger.info(f"原始資料行數: {len(df)}")
            
            # 檢查必要欄位
            required_columns = [
                self.shopify_columns['title'],
                self.shopify_columns['vendor']
            ]
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                raise ValueError(f"缺少必要欄位: {missing_columns}")
            
            # 保留需要的欄位（包括可選的 Product Category）
            columns_to_keep = required_columns.copy()
            if self.shopify_columns['product_category'] in df.columns:
                columns_to_keep.append(self.shopify_columns['product_category'])
            
            df_filtered = df[columns_to_keep].copy()
            
            # 重新命名欄位為標準名稱
            column_mapping = {v: k for k, v in self.shopify_columns.items() if v in df_filtered.columns}
            df_filtered = df_filtered.rename(columns=column_mapping)
            
            # 資料清理
            df_filtered = self._clean_shopify_data(df_filtered)
            
            logger.info(f"過濾後資料行數: {len(df_filtered)}")
            return df_filtered
            
        except Exception as e:
            logger.error(f"載入 Shopify 主清單失敗: {e}")
            raise
    
    def _clean_store_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理藥房產品資料"""
        # 移除空的產品描述
        df = df.dropna(subset=['item_description'])
        
        # 清理產品描述（移除多餘空格）
        df['item_description'] = df['item_description'].str.strip()
        
        # 處理部門欄位（品牌）
        df['department'] = df['department'].fillna('').str.strip()
        
        # 處理修改時間
        if 'modified' in df.columns:
            df['modified'] = pd.to_datetime(df['modified'], errors='coerce')
        
        # 移除完全重複的行
        df = df.drop_duplicates()
        
        return df
    
    def _clean_shopify_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理 Shopify 資料"""
        # 移除空的標題或供應商
        df = df.dropna(subset=['title', 'vendor'])
        
        # 清理文字欄位
        df['title'] = df['title'].str.strip()
        df['vendor'] = df['vendor'].str.strip()
        
        if 'product_category' in df.columns:
            df['product_category'] = df['product_category'].fillna('').str.strip()
        
        # 移除完全重複的行
        df = df.drop_duplicates()
        
        return df
    
    def get_unique_vendors(self, shopify_df: pd.DataFrame) -> List[str]:
        """取得 Shopify 清單中的所有唯一供應商"""
        return sorted(shopify_df['vendor'].unique().tolist())
    
    def validate_data_integrity(self, store_df: pd.DataFrame, shopify_df: pd.DataFrame) -> Dict[str, any]:
        """
        驗證資料完整性
        
        Returns:
            驗證結果字典
        """
        validation_results = {
            'store_list': {
                'total_rows': len(store_df),
                'empty_descriptions': store_df['item_description'].isna().sum(),
                'empty_departments': store_df['department'].eq('').sum(),
            },
            'shopify_list': {
                'total_rows': len(shopify_df),
                'unique_vendors': len(shopify_df['vendor'].unique()),
                'unique_titles': len(shopify_df['title'].unique()),
            }
        }
        
        logger.info(f"資料驗證結果: {validation_results}")
        return validation_results


if __name__ == "__main__":
    # 測試程式碼
    logging.basicConfig(level=logging.INFO)
    
    loader = DataLoader()
    
    # 測試載入（需要實際的 CSV 文件）
    try:
        store_df = loader.load_store_list("data/store_list.csv")
        shopify_df = loader.load_shopify_list("data/shopify_list.csv")
        
        validation = loader.validate_data_integrity(store_df, shopify_df)
        print("資料載入成功！")
        print(f"藥房清單: {validation['store_list']['total_rows']} 行")
        print(f"Shopify 清單: {validation['shopify_list']['total_rows']} 行")
        
    except FileNotFoundError:
        print("測試文件不存在，請先準備測試資料")
    except Exception as e:
        print(f"測試失敗: {e}")
