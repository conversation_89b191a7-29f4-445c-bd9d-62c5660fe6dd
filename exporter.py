"""
匯出器模組 - 輸出結果 CSV、審查清單和日誌
Exporter Module - Output results CSV, review list, and logs
"""

import logging
import pandas as pd
import yaml
import json
from typing import Dict, List, Any, Optional
from pathlib import Path
from datetime import datetime
import os

logger = logging.getLogger(__name__)


class ResultExporter:
    """結果匯出器，負責生成各種輸出文件"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化匯出器
        
        Args:
            config_path: 配置文件路徑
        """
        self.config = self._load_config(config_path)
        self.paths_config = self.config.get('paths', {})
        self.output_dir = self.paths_config.get('output_dir', 'output/')
        self.logs_dir = self.paths_config.get('logs_dir', 'logs/')
        
        # 確保輸出目錄存在
        self._ensure_directories()
        
        logger.info(f"匯出器初始化 - 輸出目錄: {self.output_dir}")
    
    def _load_config(self, config_path: str) -> Dict:
        """載入配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            logger.error(f"配置文件未找到: {config_path}")
            raise
        except yaml.YAMLError as e:
            logger.error(f"配置文件格式錯誤: {e}")
            raise
    
    def _ensure_directories(self):
        """確保輸出目錄存在"""
        Path(self.output_dir).mkdir(parents=True, exist_ok=True)
        Path(self.logs_dir).mkdir(parents=True, exist_ok=True)
    
    def _generate_timestamp(self) -> str:
        """生成時間戳字符串"""
        return datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def export_matched_results(self, results: List[Dict[str, Any]], 
                             filename: Optional[str] = None) -> str:
        """
        匯出匹配結果到 CSV
        
        Args:
            results: 匹配結果清單
            filename: 自定義文件名（可選）
            
        Returns:
            輸出文件路徑
        """
        if filename is None:
            timestamp = self._generate_timestamp()
            filename = f"matched_results_{timestamp}.csv"
        
        output_path = os.path.join(self.output_dir, filename)
        
        # 準備 DataFrame
        df_data = []
        
        for result in results:
            # 原始店鋪資料
            store_data = result.get('store_data', {})
            
            # 匹配的 Shopify 資料
            shopify_data = result.get('shopify_data', {})
            
            # AI 投票結果
            voting_result = result.get('voting_result', {})
            model_votes = voting_result.get('model_votes', {})
            
            # 構建行資料
            row = {
                # 原始店鋪資料
                'Item Description': store_data.get('item_description', ''),
                'Department': store_data.get('department', ''),
                'Modified': store_data.get('modified', ''),
                
                # 匹配結果
                'Matched Vendor': shopify_data.get('vendor', ''),
                'Final Title': shopify_data.get('title', ''),
                'Category': shopify_data.get('product_category', ''),
                
                # AI 投票詳情
                'GPT-4o': model_votes.get('gpt4o', {}).get('match', ''),
                'Claude': model_votes.get('claude', {}).get('match', ''),
                'Gemini': model_votes.get('gemini', {}).get('match', ''),
                
                # 最終決策
                'Final Match': voting_result.get('final_match', ''),
                'Confidence': voting_result.get('confidence', ''),
                'Needs Review': voting_result.get('needs_review', False),
                
                # 統計信息
                'Vote Count': voting_result.get('vote_count', 0),
                'Agreement Level': voting_result.get('agreement_level', ''),
                
                # 推理信息
                'AI Reasoning': voting_result.get('reasoning', '')[:500]  # 限制長度
            }
            
            df_data.append(row)
        
        # 創建 DataFrame 並匯出
        df = pd.DataFrame(df_data)
        df.to_csv(output_path, index=False, encoding='utf-8-sig')
        
        logger.info(f"匹配結果已匯出到: {output_path} ({len(df)} 行)")
        return output_path
    
    def export_review_list(self, results: List[Dict[str, Any]], 
                          filename: Optional[str] = None) -> str:
        """
        匯出需要審查的項目清單
        
        Args:
            results: 匹配結果清單
            filename: 自定義文件名（可選）
            
        Returns:
            輸出文件路徑
        """
        if filename is None:
            timestamp = self._generate_timestamp()
            filename = f"needs_review_{timestamp}.csv"
        
        output_path = os.path.join(self.output_dir, filename)
        
        # 過濾需要審查的項目
        review_items = []
        for result in results:
            voting_result = result.get('voting_result', {})
            if voting_result.get('needs_review', False):
                
                store_data = result.get('store_data', {})
                shopify_data = result.get('shopify_data', {})
                model_votes = voting_result.get('model_votes', {})
                
                review_item = {
                    'Item Description': store_data.get('item_description', ''),
                    'Department': store_data.get('department', ''),
                    'Reason for Review': self._get_review_reason(voting_result),
                    'Confidence': voting_result.get('confidence', ''),
                    'Agreement Level': voting_result.get('agreement_level', ''),
                    'Vote Count': voting_result.get('vote_count', 0),
                    'Total Models': voting_result.get('total_models', 0),
                    
                    # AI 模型的個別回應
                    'GPT-4o Match': model_votes.get('gpt4o', {}).get('match', ''),
                    'GPT-4o Reasoning': model_votes.get('gpt4o', {}).get('reasoning', ''),
                    'Claude Match': model_votes.get('claude', {}).get('match', ''),
                    'Claude Reasoning': model_votes.get('claude', {}).get('reasoning', ''),
                    'Gemini Match': model_votes.get('gemini', {}).get('match', ''),
                    'Gemini Reasoning': model_votes.get('gemini', {}).get('reasoning', ''),
                    
                    # 候選項目信息
                    'Available Candidates': self._format_candidates(result.get('fuzzy_matches', [])),
                    'Suggested Action': self._suggest_action(voting_result)
                }
                
                review_items.append(review_item)
        
        # 創建 DataFrame 並匯出
        df = pd.DataFrame(review_items)
        df.to_csv(output_path, index=False, encoding='utf-8-sig')
        
        logger.info(f"審查清單已匯出到: {output_path} ({len(df)} 行)")
        return output_path
    
    def _get_review_reason(self, voting_result: Dict[str, Any]) -> str:
        """獲取需要審查的原因"""
        confidence = voting_result.get('confidence', '')
        agreement_level = voting_result.get('agreement_level', '')
        vote_count = voting_result.get('vote_count', 0)
        total_models = voting_result.get('total_models', 0)
        
        reasons = []
        
        if confidence == 'LOW':
            reasons.append("低信心度")
        if agreement_level == 'NO_CONSENSUS':
            reasons.append("模型間無共識")
        if vote_count < 2:
            reasons.append("投票數不足")
        if total_models < 2:
            reasons.append("可用模型數不足")
        
        return "; ".join(reasons) if reasons else "未知原因"
    
    def _format_candidates(self, fuzzy_matches: List[Dict]) -> str:
        """格式化候選項目信息"""
        if not fuzzy_matches:
            return "無候選項目"
        
        candidates = []
        for match in fuzzy_matches[:3]:  # 只顯示前3個
            title = match.get('matched_title', '')
            score = match.get('primary_score', 0)
            candidates.append(f"{title} ({score:.1f})")
        
        return "; ".join(candidates)
    
    def _suggest_action(self, voting_result: Dict[str, Any]) -> str:
        """建議處理動作"""
        confidence = voting_result.get('confidence', '')
        agreement_level = voting_result.get('agreement_level', '')
        
        if agreement_level == 'NO_CONSENSUS':
            return "手動檢查所有候選項目"
        elif confidence == 'LOW':
            return "驗證匹配準確性"
        else:
            return "一般審查"
    
    def export_processing_log(self, processing_data: Dict[str, Any], 
                            filename: Optional[str] = None) -> str:
        """
        匯出處理日誌
        
        Args:
            processing_data: 處理過程資料
            filename: 自定義文件名（可選）
            
        Returns:
            輸出文件路徑
        """
        if filename is None:
            timestamp = self._generate_timestamp()
            filename = f"processing_log_{timestamp}.json"
        
        output_path = os.path.join(self.logs_dir, filename)
        
        # 添加元數據
        log_data = {
            'timestamp': datetime.now().isoformat(),
            'processing_summary': processing_data.get('summary', {}),
            'configuration': self.config,
            'detailed_log': processing_data.get('detailed_log', [])
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(log_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"處理日誌已匯出到: {output_path}")
        return output_path
    
    def export_statistics_report(self, statistics: Dict[str, Any], 
                               filename: Optional[str] = None) -> str:
        """
        匯出統計報告
        
        Args:
            statistics: 統計資料
            filename: 自定義文件名（可選）
            
        Returns:
            輸出文件路徑
        """
        if filename is None:
            timestamp = self._generate_timestamp()
            filename = f"statistics_report_{timestamp}.json"
        
        output_path = os.path.join(self.output_dir, filename)
        
        # 添加報告元數據
        report_data = {
            'report_timestamp': datetime.now().isoformat(),
            'statistics': statistics,
            'summary': {
                'total_processed': statistics.get('total_processed', 0),
                'successful_matches': statistics.get('successful_matches', 0),
                'needs_review': statistics.get('needs_review', 0),
                'success_rate': statistics.get('success_rate', 0),
                'review_rate': statistics.get('review_rate', 0)
            }
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"統計報告已匯出到: {output_path}")
        return output_path
    
    def create_summary_report(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        創建摘要報告
        
        Args:
            results: 處理結果清單
            
        Returns:
            摘要報告字典
        """
        total_processed = len(results)
        
        if total_processed == 0:
            return {
                'total_processed': 0,
                'successful_matches': 0,
                'needs_review': 0,
                'success_rate': 0,
                'review_rate': 0
            }
        
        # 統計各種結果
        successful_matches = 0
        needs_review = 0
        confidence_distribution = {'HIGH': 0, 'MEDIUM': 0, 'LOW': 0, 'VERY_LOW': 0}
        agreement_distribution = {}
        
        for result in results:
            voting_result = result.get('voting_result', {})
            
            if not voting_result.get('needs_review', False):
                successful_matches += 1
            else:
                needs_review += 1
            
            # 統計信心度分佈
            confidence = voting_result.get('confidence', 'VERY_LOW')
            confidence_distribution[confidence] = confidence_distribution.get(confidence, 0) + 1
            
            # 統計協議水平分佈
            agreement = voting_result.get('agreement_level', 'UNKNOWN')
            agreement_distribution[agreement] = agreement_distribution.get(agreement, 0) + 1
        
        success_rate = successful_matches / total_processed
        review_rate = needs_review / total_processed
        
        return {
            'total_processed': total_processed,
            'successful_matches': successful_matches,
            'needs_review': needs_review,
            'success_rate': success_rate,
            'review_rate': review_rate,
            'confidence_distribution': confidence_distribution,
            'agreement_distribution': agreement_distribution,
            'timestamp': datetime.now().isoformat()
        }
    
    def export_all_outputs(self, results: List[Dict[str, Any]], 
                          processing_data: Optional[Dict[str, Any]] = None) -> Dict[str, str]:
        """
        匯出所有輸出文件
        
        Args:
            results: 處理結果清單
            processing_data: 處理過程資料（可選）
            
        Returns:
            輸出文件路徑字典
        """
        timestamp = self._generate_timestamp()
        
        output_files = {}
        
        # 匯出主要結果
        output_files['matched_results'] = self.export_matched_results(
            results, f"matched_results_{timestamp}.csv"
        )
        
        # 匯出審查清單
        output_files['review_list'] = self.export_review_list(
            results, f"needs_review_{timestamp}.csv"
        )
        
        # 創建並匯出統計報告
        statistics = self.create_summary_report(results)
        output_files['statistics'] = self.export_statistics_report(
            statistics, f"statistics_{timestamp}.json"
        )
        
        # 匯出處理日誌（如果提供）
        if processing_data:
            output_files['processing_log'] = self.export_processing_log(
                processing_data, f"processing_log_{timestamp}.json"
            )
        
        logger.info(f"所有輸出文件已匯出，時間戳: {timestamp}")
        return output_files


if __name__ == "__main__":
    # 測試程式碼
    logging.basicConfig(level=logging.INFO)
    
    # 創建測試資料
    test_results = [
        {
            'store_data': {
                'item_description': 'SW Vit C 1000mg Tab 60',
                'department': 'Vitamins',
                'modified': '2024-01-01'
            },
            'shopify_data': {
                'vendor': 'Swisse',
                'title': 'Swisse Vitamin C 1000mg Tablets 60 Pack',
                'product_category': 'Vitamins'
            },
            'voting_result': {
                'final_match': 'Swisse Vitamin C 1000mg Tablets 60 Pack',
                'confidence': 'HIGH',
                'needs_review': False,
                'vote_count': 3,
                'agreement_level': 'STRONG_CONSENSUS',
                'model_votes': {
                    'gpt4o': {'match': 'Swisse Vitamin C 1000mg Tablets 60 Pack', 'reasoning': 'Perfect match'},
                    'claude': {'match': 'Swisse Vitamin C 1000mg Tablets 60 Pack', 'reasoning': 'Clear match'},
                    'gemini': {'match': 'Swisse Vitamin C 1000mg Tablets 60 Pack', 'reasoning': 'Exact match'}
                }
            }
        },
        {
            'store_data': {
                'item_description': 'Unknown Product',
                'department': '',
                'modified': '2024-01-02'
            },
            'shopify_data': {},
            'voting_result': {
                'final_match': None,
                'confidence': 'LOW',
                'needs_review': True,
                'vote_count': 0,
                'agreement_level': 'NO_CONSENSUS',
                'model_votes': {}
            }
        }
    ]
    
    # 創建匯出器並測試
    exporter = ResultExporter()
    
    print("測試匯出功能...")
    output_files = exporter.export_all_outputs(test_results)
    
    print("匯出的文件:")
    for file_type, file_path in output_files.items():
        print(f"  {file_type}: {file_path}")
    
    # 顯示摘要報告
    summary = exporter.create_summary_report(test_results)
    print(f"\n摘要報告:")
    print(f"  總處理數: {summary['total_processed']}")
    print(f"  成功匹配: {summary['successful_matches']}")
    print(f"  需要審查: {summary['needs_review']}")
    print(f"  成功率: {summary['success_rate']:.2%}")
    print(f"  審查率: {summary['review_rate']:.2%}")
